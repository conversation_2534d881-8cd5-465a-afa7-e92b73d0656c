'use client';

import { ComplaintInformationForm } from '@/features/complaints/components/complaint-information-form';
import { useRouter } from 'next/navigation';

export default function NewComplaintPage() {
  const router = useRouter();

  const handleSuccess = () => {
    // Redirect back to complaints list with success message
    router.push('/complaints?success=created');
  };
  const handleCancel = () => {
    // Navigate to complaints dashboard
    router.push('/complaints');
  };

  return (
    <ComplaintInformationForm
      onSuccess={handleSuccess}
      onCancel={handleCancel}
    />
  );
}
