'use client';

import {
  ComplaintsWidget,
  MaintenanceLogsWidget,
  PMACertificatesWidget,
  StatsOverview,
} from '@/components/dashboard';
import { Badge } from '@/components/ui/badge';
import { useProject } from '@/features/projects';
import { useUserWithProfile } from '@/hooks';
import { usePermissions } from '@/hooks/use-permissions';
import { useProjectContext } from '@/providers/project-context';
import { useRouter } from 'next/navigation';

export default function DashboardPage() {
  const { data: user, isLoading, error } = useUserWithProfile();
  const { userRole } = usePermissions();
  const { selectedProjectId, isInProjectContext } = useProjectContext();
  const { data: _project, isLoading: projectLoading } = useProject(
    selectedProjectId || '',
  );
  const router = useRouter();

  if (isLoading || (isInProjectContext && projectLoading)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">
          {isInProjectContext
            ? 'Loading project dashboard...'
            : 'Loading dashboard...'}
        </div>
      </div>
    );
  }

  if (error || !user) {
    router.push('/login');
    return null;
  }

  // Regular dashboard when not in project context
  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            Welcome, {user.profile?.name || user.email?.split('@')[0]}!
          </h1>
          <p className="text-muted-foreground mt-1">
            Here&apos;s what&apos;s happening with your project today.
          </p>
          {userRole && (
            <Badge variant="secondary" className="mt-2">
              {userRole}
            </Badge>
          )}
        </div>
      </div>

      {/* Stats Overview */}
      <div className="mb-6">
        <StatsOverview />
      </div>

      {/* Overview Dashboard Widgets */}
      <div className="grid grid-cols-1 gap-8 mb-8">
        {/* PMA Certificates Widget */}
        <PMACertificatesWidget />

        {/* Maintenance Logs Widget */}
        <MaintenanceLogsWidget />

        {/* Complaints Widget */}
        <ComplaintsWidget />
      </div>
    </div>
  );
}
