'use client';

import { But<PERSON> } from '@/components/ui/button';
import { PmaForm } from '@/features/pma-management/components/pma-form';
import { useCreatePMACertificate } from '@/features/pma-management/hooks/use-create-pma-certificate';
import { useProjectContext } from '@/providers/project-context';
import { FileText } from 'lucide-react';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';

export default function AddPma() {
  const { selectedProjectId } = useProjectContext();
  const { mutate, isPending } = useCreatePMACertificate();
  const router = useRouter();
  const params = useParams();
  const locale = params.locale as string;

  const handleSubmit = (data: {
    pmas: { location: string; pmaNumber: string; dateReceived: string }[];
  }) => {
    if (!selectedProjectId) {
      console.error('Project ID is not selected.');
      return;
    }

    data.pmas.forEach((pma) => {
      mutate({
        project_id: selectedProjectId,
        pma_number: pma.pmaNumber,
        location: pma.location,
        expiry_date: new Date(
          new Date(pma.dateReceived).setFullYear(
            new Date(pma.dateReceived).getFullYear() + 1,
          ),
        )
          .toISOString()
          .split('T')[0],
        status: 'valid',
      });
    });
    router.push(`/${locale}/pmas`);
  };

  return (
    <div className="bg-gradient-to-br from-slate-50 via-white to-slate-50/50">
      {/* Enhanced Header with Breadcrumbs */}
      <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-lg border-b border-slate-200/60">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Breadcrumbs */}
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Link
                href="/pmas"
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>PMA Management</span>
                </div>
              </Link>
              <span className="text-muted-foreground">/</span>
              <span className="text-primary font-medium">Add New PMA</span>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => router.push(`/${locale}/pmas`)}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              Add New PMA Entry
            </h1>
            <p className="mt-2 text-lg text-muted-foreground">
              Add one or more PMA entries for your project.
            </p>
          </div>

          {/* Form */}
          <PmaForm onSubmit={handleSubmit} isLoading={isPending} />
        </div>
      </div>
    </div>
  );
}
