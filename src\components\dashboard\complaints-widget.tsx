'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { DonutChart } from '@/components/ui/charts';
import { useComplaintsStats } from '@/features/dashboard/hooks';
import { useMemo } from 'react';
import { cn } from '@/lib/utils';
import {
  Activity,
  AlertTriangle,
  ArrowRight,
  CheckCircle,
  Clock,
  Gauge,
  MessagesSquare,
  PlayCircle,
  PlusCircle,
  TrendingDown,
  TrendingUp,
  XCircle,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import {
  Area,
  AreaChart as RechartsAreaChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Legend,
} from 'recharts';

export function ComplaintsWidget() {
  const { data: complaintsStats, isLoading } = useComplaintsStats();
  const router = useRouter();

  // Calculate total complaints
  const totalComplaints =
    (complaintsStats?.newCount || 0) +
    (complaintsStats?.inProgressCount || 0) +
    (complaintsStats?.resolvedCount || 0) +
    (complaintsStats?.closedCount || 0);

  // Define pastel color for a minimalist shadcn style
  const chartColors = {
    total: '#60a5fa', // blue-400 - slightly more vibrant blue
    gradient: {
      start: '#dbeafe', // blue-100 - very light blue for better gradient
      end: '#3b82f6', // blue-500
    },
    new: '#38bdf8', // sky-400
    inProgress: '#a78bfa', // violet-400
    resolved: '#4ade80', // green-400
    closed: '#f87171', // red-400
  };

  // Configure chart to show separate lines instead of stacked areas

  // We'll directly use the chart colors for better visibility
  // No need for CSS variables which might cause problems

  // Get data from the service
  const [timeRange, setTimeRange] = useState<'6months' | '12months'>('6months');

  // Format trendAnalysis data for the area chart
  const complaintsAreaData = useMemo(() => {
    // If we have stats data but no trend analysis data, provide default months
    if (
      complaintsStats &&
      (!complaintsStats.trendAnalysis ||
        complaintsStats.trendAnalysis.length === 0)
    ) {
      const months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];
      // Create placeholder data with month names but zero values
      return Array(timeRange === '6months' ? 6 : 12)
        .fill(0)
        .map((_, i) => {
          const monthIndex =
            timeRange === '6months'
              ? (new Date().getMonth() - 5 + i + 12) % 12 // Last 6 months
              : i;
          return {
            month: months[monthIndex],
            total: 0,
          };
        });
    }

    // If we don't have any stats data yet, return empty array
    if (!complaintsStats?.trendAnalysis) {
      return [];
    }

    const trendData = [...complaintsStats.trendAnalysis];
    return timeRange === '6months'
      ? trendData.slice(6, 12) // Last 6 months
      : trendData; // All 12 months
  }, [complaintsStats, timeRange]);

  // Get only 3 recent complaints for the side panel
  const recentComplaints = complaintsStats?.recent?.slice(0, 3) || [];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Complaints</h2>
        <Button
          variant="ghost"
          size="sm"
          className="gap-1 hover:bg-blue-50 transition-colors"
          onClick={() => router.push('/complaints')}
        >
          View All <ArrowRight className="h-4 w-4" />
        </Button>
      </div>

      {isLoading ? (
        <div className="h-60 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <div className="animate-spin w-10 h-10 border-4 border-blue-200 border-t-blue-600 rounded-full mb-3"></div>
            <p className="text-muted-foreground">Loading complaints data...</p>
          </div>
        </div>
      ) : (
        <>
          {/* Top row: Status chart and Recent complaints side by side */}
          <div className="grid gap-4 md:grid-cols-2">
            {/* Enhanced Complaint Status Chart */}
            <Card className="overflow-hidden border border-gray-100 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-blue-50/80 to-indigo-50/50">
              <CardHeader className="pb-2 border-b border-gray-100">
                <CardTitle className="text-base font-medium flex items-center gap-2 text-blue-800">
                  <Gauge className="h-4 w-4 text-blue-600" />
                  Complaint Status Distribution
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                {totalComplaints === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8 px-4 text-center space-y-3 h-[250px]">
                    <div className="relative">
                      <MessagesSquare className="h-16 w-16 text-gray-200" />
                      <PlusCircle className="h-6 w-6 text-blue-500 absolute bottom-0 right-0" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-1">
                        No Complaints Data
                      </h3>
                      <p className="text-xs text-gray-500 mb-3">
                        There are no complaints in the system yet.
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-blue-200 text-blue-600 hover:bg-blue-50"
                        onClick={() => router.push('/complaints/create')}
                      >
                        Create Complaint
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col space-y-3">
                    {/* Chart with Total Count in Center */}
                    <div className="relative">
                      <DonutChart
                        data={[
                          {
                            name: 'New',
                            value: complaintsStats?.newCount || 0,
                            color: chartColors.new,
                          },
                          {
                            name: 'In Progress',
                            value: complaintsStats?.inProgressCount || 0,
                            color: chartColors.inProgress,
                          },
                          {
                            name: 'Resolved',
                            value: complaintsStats?.resolvedCount || 0,
                            color: chartColors.resolved,
                          },
                          {
                            name: 'Closed',
                            value: complaintsStats?.closedCount || 0,
                            color: chartColors.closed,
                          },
                        ]}
                        size="sm"
                        innerRadius={60}
                        outerRadius={78}
                        centerLabel={
                          <g>
                            <text
                              x="50%"
                              y="50%"
                              textAnchor="middle"
                              dominantBaseline="middle"
                              className="fill-gray-400 text-xs font-normal"
                              dy="-10"
                            >
                              Total
                            </text>
                            <text
                              x="50%"
                              y="50%"
                              textAnchor="middle"
                              dominantBaseline="middle"
                              className="fill-gray-900 text-xl font-bold"
                              dy="12"
                            >
                              {totalComplaints}
                            </text>
                          </g>
                        }
                        showLegend={false}
                        showTooltip={true}
                      />
                    </div>

                    {/* Modern status cards grid */}
                    <div className="grid grid-cols-2 gap-2">
                      <div className="flex items-center bg-white rounded-lg p-2 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 cursor-pointer">
                        <div className="p-1.5 bg-blue-100 rounded-full mr-2">
                          <PlusCircle className="h-3.5 w-3.5 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-center">
                            <span className="text-xs font-medium text-gray-500">
                              New
                            </span>
                            <span className="text-sm font-bold text-gray-800">
                              {complaintsStats?.newCount || 0}
                            </span>
                          </div>
                          <div className="mt-1">
                            <div className="h-1.5 bg-blue-100 rounded-full relative overflow-hidden">
                              <div
                                className="absolute inset-y-0 bg-blue-500 rounded-full"
                                style={{
                                  width: `${totalComplaints ? ((complaintsStats?.newCount || 0) / totalComplaints) * 100 : 0}%`,
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center bg-white rounded-lg p-2 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 cursor-pointer">
                        <div className="p-1.5 bg-amber-100 rounded-full mr-2">
                          <PlayCircle className="h-3.5 w-3.5 text-amber-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-center">
                            <span className="text-xs font-medium text-gray-500">
                              In Progress
                            </span>
                            <span className="text-sm font-bold text-gray-800">
                              {complaintsStats?.inProgressCount || 0}
                            </span>
                          </div>
                          <div className="mt-1">
                            <div className="h-1.5 bg-amber-100 rounded-full relative overflow-hidden">
                              <div
                                className="absolute inset-y-0 bg-amber-500 rounded-full"
                                style={{
                                  width: `${totalComplaints ? ((complaintsStats?.inProgressCount || 0) / totalComplaints) * 100 : 0}%`,
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center bg-white rounded-lg p-2 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 cursor-pointer">
                        <div className="p-1.5 bg-emerald-100 rounded-full mr-2">
                          <CheckCircle className="h-3.5 w-3.5 text-emerald-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-center">
                            <span className="text-xs font-medium text-gray-500">
                              Resolved
                            </span>
                            <span className="text-sm font-bold text-gray-800">
                              {complaintsStats?.resolvedCount || 0}
                            </span>
                          </div>
                          <div className="mt-1">
                            <div className="h-1.5 bg-emerald-100 rounded-full relative overflow-hidden">
                              <div
                                className="absolute inset-y-0 bg-emerald-500 rounded-full"
                                style={{
                                  width: `${totalComplaints ? ((complaintsStats?.resolvedCount || 0) / totalComplaints) * 100 : 0}%`,
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center bg-white rounded-lg p-2 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 cursor-pointer">
                        <div className="p-1.5 bg-gray-100 rounded-full mr-2">
                          <XCircle className="h-3.5 w-3.5 text-gray-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-center">
                            <span className="text-xs font-medium text-gray-500">
                              Closed
                            </span>
                            <span className="text-sm font-bold text-gray-800">
                              {complaintsStats?.closedCount || 0}
                            </span>
                          </div>
                          <div className="mt-1">
                            <div className="h-1.5 bg-gray-100 rounded-full relative overflow-hidden">
                              <div
                                className="absolute inset-y-0 bg-gray-500 rounded-full"
                                style={{
                                  width: `${totalComplaints ? ((complaintsStats?.closedCount || 0) / totalComplaints) * 100 : 0}%`,
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Complaints - Top 3 */}
            <Card className="overflow-hidden border border-gray-100 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-gray-50/80 to-white">
              <CardHeader className="pb-2 border-b border-gray-100">
                <CardTitle className="text-base font-medium flex items-center gap-2 text-slate-800">
                  <MessagesSquare className="h-4 w-4 text-blue-600" />
                  Recent Complaints
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                {isLoading ? (
                  <div className="h-[250px] flex items-center justify-center">
                    <div className="flex flex-col items-center">
                      <div className="animate-spin w-8 h-8 border-3 border-blue-200 border-t-blue-600 rounded-full mb-2"></div>
                      <p className="text-sm text-muted-foreground">
                        Loading recent complaints...
                      </p>
                    </div>
                  </div>
                ) : recentComplaints.length > 0 ? (
                  <div className="divide-y divide-gray-100 h-[250px] flex flex-col justify-center">
                    {recentComplaints.map((complaint) => (
                      <div
                        key={complaint.id}
                        className="flex items-center justify-between py-3 px-2 hover:bg-blue-50/30 rounded-lg transition-colors group cursor-pointer"
                      >
                        <div className="flex items-start gap-3">
                          <div
                            className={cn(
                              'rounded-full p-2 shadow-sm transition-transform group-hover:scale-110',
                              complaint.status === 'resolved'
                                ? 'bg-emerald-100'
                                : complaint.status === 'new'
                                  ? 'bg-blue-100'
                                  : complaint.status === 'in_progress'
                                    ? 'bg-amber-100'
                                    : 'bg-gray-100',
                            )}
                          >
                            <MessagesSquare
                              className={cn(
                                'h-4 w-4',
                                complaint.status === 'resolved'
                                  ? 'text-emerald-600'
                                  : complaint.status === 'new'
                                    ? 'text-blue-600'
                                    : complaint.status === 'in_progress'
                                      ? 'text-amber-600'
                                      : 'text-gray-600',
                              )}
                            />
                          </div>
                          <div>
                            <p className="font-medium text-sm text-gray-900 group-hover:text-blue-700 transition-colors truncate max-w-[180px]">
                              {complaint.title}
                            </p>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span className="text-xs">
                                  {complaint.date}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <Badge
                          variant={
                            complaint.status === 'resolved'
                              ? 'success'
                              : complaint.status === 'new'
                                ? 'default'
                                : complaint.status === 'in_progress'
                                  ? 'warning'
                                  : 'secondary'
                          }
                          className="shadow-sm"
                        >
                          {complaint.status === 'new'
                            ? 'New'
                            : complaint.status === 'in_progress'
                              ? 'In Progress'
                              : complaint.status === 'resolved'
                                ? 'Resolved'
                                : complaint.status === 'closed'
                                  ? 'Closed'
                                  : complaint.status}
                        </Badge>
                      </div>
                    ))}
                    {recentComplaints.length < 3 && (
                      <div className="text-center mt-auto mb-auto py-4">
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-blue-200 text-blue-600 hover:bg-blue-50"
                          onClick={() => router.push('/complaints/create')}
                        >
                          Add Complaint
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-10 px-6 text-center space-y-4 h-[250px]">
                    <div className="relative">
                      <MessagesSquare className="h-16 w-16 text-gray-200" />
                      <AlertTriangle className="h-6 w-6 text-blue-500 absolute bottom-0 right-0" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium mb-1">
                        No Complaints Found
                      </h3>
                      <p className="text-xs text-gray-500 mb-3">
                        Get started by creating your first complaint.
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-blue-200 text-blue-600 hover:bg-blue-50"
                        onClick={() => router.push('/complaints/create')}
                      >
                        Create Complaint
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Bottom row: Full-width area chart using shadcn style */}
          <Card className="overflow-hidden bg-gradient-to-br from-white to-blue-50/30 border border-blue-100 shadow-md rounded-xl">
            <CardHeader className="pb-2 border-b border-blue-50 bg-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Activity className="h-4 w-4 text-blue-500" />
                  <CardTitle className="text-blue-700 font-semibold flex items-center gap-1.5 text-base">
                    Complaints Trend Analysis
                    <span className="bg-blue-100 text-blue-700 text-[9px] font-medium px-1.5 py-0.5 rounded-full">
                      ANALYTICS
                    </span>
                  </CardTitle>
                </div>
                <div className="flex items-center gap-1 bg-blue-50 rounded-full p-0.5 border border-blue-100 shadow-inner backdrop-blur-sm">
                  <button
                    onClick={() => setTimeRange('6months')}
                    className={`text-xs px-4 py-1.5 rounded-full font-medium transition-all ${
                      timeRange === '6months'
                        ? 'bg-white shadow-md text-blue-600 ring-1 ring-blue-200'
                        : 'text-blue-600/70 hover:bg-blue-50 hover:text-blue-700'
                    }`}
                  >
                    6 Months
                  </button>
                  <button
                    onClick={() => setTimeRange('12months')}
                    className={`text-xs px-4 py-1.5 rounded-full font-medium transition-all ${
                      timeRange === '12months'
                        ? 'bg-white shadow-md text-blue-600 ring-1 ring-blue-200'
                        : 'text-blue-600/70 hover:bg-blue-50 hover:text-blue-700'
                    }`}
                  >
                    12 Months
                  </button>
                </div>
              </div>
              <CardDescription className="mt-1.5 text-blue-600/70 italic text-xs">
                {timeRange === '6months'
                  ? 'Overview of total complaints for the last 6 months (Jan-Jun 2024)'
                  : 'Overview of total complaints over the past 12 months (Jan 2023-Dec 2024)'}
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              {isLoading ? (
                <div className="h-[400px] flex items-center justify-center">
                  <div className="flex flex-col items-center">
                    <div className="animate-spin w-8 h-8 border-3 border-cyan-200 border-t-cyan-600 rounded-full mb-2"></div>
                    <p className="text-sm text-muted-foreground">
                      Loading data...
                    </p>
                  </div>
                </div>
              ) : complaintsAreaData.every((item) => item.total === 0) ? (
                <div className="h-[400px] flex items-center justify-center">
                  <div className="flex flex-col items-center text-center">
                    <div className="bg-blue-50 p-4 rounded-full mb-4">
                      <MessagesSquare className="h-12 w-12 text-blue-300" />
                    </div>
                    <p className="text-base font-medium text-gray-600 mb-2">
                      No Complaints Reported
                    </p>
                    <p className="text-sm text-gray-500 mb-4 max-w-xs">
                      There are no complaints in the selected time period. This
                      could be a good sign!
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-blue-200 text-blue-600 hover:bg-blue-50"
                      onClick={() => router.push('/complaints/create')}
                    >
                      <PlusCircle className="h-4 w-4 mr-1" />
                      Create Complaint
                    </Button>
                  </div>
                </div>
              ) : (
                <div className=" mt-4 bg-white">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsAreaChart
                      accessibilityLayer
                      data={complaintsAreaData}
                      margin={{
                        top: 16,
                        right: 16,
                        left: 10,
                        bottom: 0,
                      }}
                    >
                      <CartesianGrid
                        vertical={false}
                        strokeDasharray="3 8"
                        stroke="#f1f5f9"
                        strokeOpacity={0.5}
                      />
                      <XAxis
                        dataKey="month"
                        tickLine={false}
                        axisLine={false}
                        tickMargin={8}
                        tickFormatter={(value) => value.slice(0, 3)}
                        tick={{ fill: '#94a3b8', fontSize: 11 }}
                      />
                      <YAxis
                        tickLine={false}
                        axisLine={false}
                        tickMargin={8}
                        tick={{ fill: '#94a3b8', fontSize: 11 }}
                        tickFormatter={(value) => value.toString()}
                      />
                      <Tooltip
                        formatter={(value, name) => [`${value}`, name]}
                        labelFormatter={(label) => label}
                        contentStyle={{
                          backgroundColor: 'white',
                          border: '1px solid #f1f5f9',
                          borderRadius: '8px',
                          padding: '10px 14px',
                          boxShadow: '0 4px 8px -2px rgba(0, 0, 0, 0.05)',
                          fontSize: '11px',
                          fontFamily: 'Inter, system-ui, sans-serif',
                        }}
                      />
                      <defs>
                        <linearGradient
                          id="totalGradient"
                          x1="0"
                          y1="0"
                          x2="0"
                          y2="1"
                        >
                          <stop
                            offset="0%"
                            stopColor={chartColors.gradient.start}
                            stopOpacity={0.9}
                          />
                          <stop
                            offset="80%"
                            stopColor={chartColors.gradient.end}
                            stopOpacity={0.1}
                          />
                        </linearGradient>
                      </defs>
                      <Area
                        dataKey="total"
                        type="monotone"
                        fill="url(#totalGradient)"
                        fillOpacity={1}
                        stroke={chartColors.total}
                        strokeWidth={2.5}
                        dot={false}
                        activeDot={{
                          r: 7,
                          strokeWidth: 2,
                          fill: '#fff',
                          stroke: '#3b82f6',
                        }}
                        isAnimationActive={true}
                        animationDuration={1800}
                        animationEasing="ease-in-out"
                        name="Total Complaints"
                      />
                      <Legend
                        verticalAlign="top"
                        height={24}
                        iconType="circle"
                        iconSize={8}
                        formatter={(value) => (
                          <span
                            style={{
                              color: '#3b82f6',
                              fontSize: '12px',
                              fontWeight: 600,
                              backgroundColor: '#eff6ff',
                              padding: '2px 8px',
                              borderRadius: '12px',
                            }}
                          >
                            {value}
                          </span>
                        )}
                        wrapperStyle={{
                          marginBottom: '8px',
                          fontFamily: 'Inter, system-ui, sans-serif',
                        }}
                      />
                    </RechartsAreaChart>
                  </ResponsiveContainer>
                </div>
              )}
            </CardContent>
            <CardFooter className="border-t border-blue-50 bg-white pt-3 pb-3">
              <div className="flex w-full items-center justify-between gap-2 text-sm">
                <div className="grid gap-1">
                  <div className="flex items-center gap-1.5 leading-none font-medium text-xs text-gray-700 py-1 px-3 rounded-full shadow-sm border border-blue-100/50">
                    {complaintsStats?.total === 0 ? (
                      <Activity className="h-3.5 w-3.5 text-gray-400" />
                    ) : complaintsStats?.trendGrowthRate !== undefined &&
                      complaintsStats.trendGrowthRate > 0 ? (
                      <TrendingUp className="h-3.5 w-3.5 text-blue-500" />
                    ) : complaintsStats?.trendGrowthRate !== undefined &&
                      complaintsStats.trendGrowthRate < 0 ? (
                      <TrendingDown className="h-3.5 w-3.5 text-red-500" />
                    ) : (
                      <Activity className="h-3.5 w-3.5 text-gray-500" />
                    )}
                    <span
                      className={`font-semibold py-0.5 px-1.5 rounded-full ${
                        complaintsStats?.total === 0
                          ? 'text-gray-500'
                          : complaintsStats?.trendGrowthRate !== undefined &&
                              complaintsStats.trendGrowthRate >= 0
                            ? 'text-blue-600'
                            : 'text-red-600'
                      }`}
                    >
                      {complaintsStats?.total === 0
                        ? 'No data'
                        : complaintsStats?.trendGrowthRate === undefined
                          ? 'No data'
                          : complaintsStats.trendGrowthRate === 0
                            ? 'No change'
                            : `${complaintsStats.trendGrowthRate > 0 ? '↑' : '↓'} ${Math.abs(complaintsStats.trendGrowthRate)}%`}
                    </span>
                    <span className="text-gray-500 text-[9px]">
                      this period
                    </span>
                  </div>
                  <div className="text-gray-400 text-[10px] flex items-center gap-1 leading-none ml-1">
                    {!isLoading
                      ? timeRange === '12months'
                        ? `${complaintsAreaData[0]?.month || 'January'} - ${complaintsAreaData[complaintsAreaData.length - 1]?.month || 'December'}`
                        : `Last 6 months`
                      : 'Loading...'}
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div
                    className={`flex items-center gap-2 bg-white px-3 py-1.5 rounded-lg shadow-sm ring-1 ${
                      complaintsStats?.total === 0
                        ? 'ring-gray-100'
                        : 'ring-blue-100'
                    } border border-white/80`}
                  >
                    <div
                      className={`w-2 h-2 rounded-full shadow-sm ${
                        complaintsStats?.total === 0 ? '' : 'animate-pulse'
                      }`}
                      style={{
                        backgroundColor:
                          complaintsStats?.total === 0
                            ? '#d1d5db'
                            : chartColors.gradient.end,
                      }}
                    ></div>
                    <span
                      className={`font-bold text-sm tracking-wide ${
                        complaintsStats?.total === 0
                          ? 'text-gray-500'
                          : 'text-blue-600'
                      }`}
                    >
                      {(complaintsStats?.total || 0).toLocaleString()}
                    </span>
                    <span
                      className={`text-[9px] uppercase tracking-wider ${
                        complaintsStats?.total === 0
                          ? 'text-gray-400'
                          : 'text-blue-500'
                      } font-medium`}
                    >
                      complaints
                    </span>
                  </div>
                </div>
              </div>
            </CardFooter>
          </Card>
        </>
      )}
    </div>
  );
}
