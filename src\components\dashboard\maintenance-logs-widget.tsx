'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DonutChart } from '@/components/ui/charts';
import {
  useMaintenanceLogsStats,
  usePMACertificatesStats,
} from '@/features/dashboard/hooks';
import { cn } from '@/lib/utils';
import {
  Activity,
  AlertTriangle,
  ArrowRight,
  BarChart3,
  CheckCircle,
  ClipboardCheck,
  ClipboardList,
  Clock,
  HelpCircle,
  Tag,
  Timer,
  XCircle,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

export function MaintenanceLogsWidget() {
  const { data: maintenanceStats, isLoading } = useMaintenanceLogsStats();
  const { data: _pmaStats } = usePMACertificatesStats();
  const router = useRouter();

  // Calculate total maintenance logs
  const totalMaintenanceLogs =
    (maintenanceStats?.completedCount || 0) +
    (maintenanceStats?.pendingCount || 0) +
    (maintenanceStats?.overdueCount || 0);

  // Define modern colors for the donut chart
  const chartColors = {
    completed: '#10b981', // emerald-500
    pending: '#f59e0b', // amber-500
    overdue: '#ef4444', // red-500
  };

  // Mock data for after-hours maintenance chart (past 6 months)
  const _afterHoursData = [
    { month: 'Jan', afterHours: 14, missed: 3 },
    { month: 'Feb', afterHours: 22, missed: 5 },
    { month: 'Mar', afterHours: 18, missed: 2 },
    { month: 'Apr', afterHours: 16, missed: 4 },
    { month: 'May', afterHours: 25, missed: 6 },
    { month: 'Jun', afterHours: 20, missed: 3 },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Maintenance Logs</h2>
        <Button
          variant="ghost"
          size="sm"
          className="gap-1 hover:bg-blue-50 transition-colors"
          onClick={() => router.push('/maintenance-logs')}
        >
          View All <ArrowRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Charts section */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Enhanced Maintenance Status Chart */}
        <Card className="overflow-hidden border border-gray-100 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-blue-50/80 to-indigo-50/50">
          <CardHeader className="pb-2 border-b border-gray-100">
            <CardTitle className="text-base font-medium flex items-center gap-2 text-blue-800">
              <Activity className="h-4 w-4 text-blue-600" />
              Maintenance Status
            </CardTitle>
          </CardHeader>
          <CardContent className="p-5">
            {isLoading ? (
              <div className="flex items-center justify-center h-[200px]">
                <div className="flex flex-col items-center">
                  <div className="animate-spin w-8 h-8 border-3 border-blue-200 border-t-blue-600 rounded-full mb-2"></div>
                  <p className="text-sm text-muted-foreground">
                    Loading status data...
                  </p>
                </div>
              </div>
            ) : totalMaintenanceLogs === 0 ? (
              <div className="flex flex-col items-center justify-center py-10 px-6 text-center space-y-4 h-[200px]">
                <div className="relative">
                  <BarChart3 className="h-16 w-16 text-gray-200" />
                  <HelpCircle className="h-6 w-6 text-blue-500 absolute bottom-0 right-0" />
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-1">
                    No Maintenance Data
                  </h3>
                  <p className="text-xs text-gray-500 mb-3">
                    There are no maintenance logs in the system yet.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-200 text-blue-600 hover:bg-blue-50"
                    onClick={() => router.push('/maintenance-logs/create')}
                  >
                    Add Maintenance Log
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col space-y-4">
                {/* Chart with Total Count in Center */}
                <div className="relative">
                  <DonutChart
                    data={[
                      {
                        name: 'Completed',
                        value: maintenanceStats?.completedCount || 0,
                        color: chartColors.completed,
                      },
                      {
                        name: 'Pending',
                        value: maintenanceStats?.pendingCount || 0,
                        color: chartColors.pending,
                      },
                      {
                        name: 'Overdue',
                        value: maintenanceStats?.overdueCount || 0,
                        color: chartColors.overdue,
                      },
                    ]}
                    size="sm"
                    innerRadius={60}
                    outerRadius={78}
                    centerLabel={
                      <g>
                        <text
                          x="50%"
                          y="50%"
                          textAnchor="middle"
                          dominantBaseline="middle"
                          className="fill-gray-400 text-xs font-normal"
                          dy="-10"
                        >
                          Total
                        </text>
                        <text
                          x="50%"
                          y="50%"
                          textAnchor="middle"
                          dominantBaseline="middle"
                          className="fill-gray-900 text-xl font-bold"
                          dy="12"
                        >
                          {totalMaintenanceLogs}
                        </text>
                      </g>
                    }
                    showLegend={false}
                    showTooltip={true}
                  />
                </div>

                {/* Modern stat cards below chart */}
                <div className="grid grid-cols-3 gap-3 mt-1">
                  <div className="flex flex-col bg-white rounded-lg p-3 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 cursor-pointer">
                    <div className="flex justify-between items-center mb-1">
                      <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                      <CheckCircle className="h-3.5 w-3.5 text-emerald-500" />
                    </div>
                    <span className="text-xs font-medium text-gray-500">
                      Completed
                    </span>
                    <span className="text-lg font-bold text-gray-800 mt-0.5">
                      {maintenanceStats?.completedCount || 0}
                    </span>
                    {maintenanceStats?.completedCount ? (
                      <div className="flex items-center gap-1 mt-1">
                        <div
                          className={cn(
                            'h-1 bg-emerald-100 rounded-full flex-grow',
                            totalMaintenanceLogs > 0 &&
                              'relative overflow-hidden',
                          )}
                        >
                          {totalMaintenanceLogs > 0 && (
                            <div
                              className="absolute inset-y-0 bg-emerald-500 rounded-full"
                              style={{
                                width: `${(maintenanceStats.completedCount / totalMaintenanceLogs) * 100}%`,
                              }}
                            ></div>
                          )}
                        </div>
                      </div>
                    ) : null}
                  </div>

                  <div className="flex flex-col bg-white rounded-lg p-3 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 cursor-pointer">
                    <div className="flex justify-between items-center mb-1">
                      <div className="w-2 h-2 rounded-full bg-amber-500"></div>
                      <Timer className="h-3.5 w-3.5 text-amber-500" />
                    </div>
                    <span className="text-xs font-medium text-gray-500">
                      Pending
                    </span>
                    <span className="text-lg font-bold text-gray-800 mt-0.5">
                      {maintenanceStats?.pendingCount || 0}
                    </span>
                    {maintenanceStats?.pendingCount ? (
                      <div className="flex items-center gap-1 mt-1">
                        <div
                          className={cn(
                            'h-1 bg-amber-100 rounded-full flex-grow',
                            totalMaintenanceLogs > 0 &&
                              'relative overflow-hidden',
                          )}
                        >
                          {totalMaintenanceLogs > 0 && (
                            <div
                              className="absolute inset-y-0 bg-amber-500 rounded-full"
                              style={{
                                width: `${(maintenanceStats.pendingCount / totalMaintenanceLogs) * 100}%`,
                              }}
                            ></div>
                          )}
                        </div>
                      </div>
                    ) : null}
                  </div>

                  <div className="flex flex-col bg-white rounded-lg p-3 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 cursor-pointer">
                    <div className="flex justify-between items-center mb-1">
                      <div className="w-2 h-2 rounded-full bg-red-500"></div>
                      <XCircle className="h-3.5 w-3.5 text-red-500" />
                    </div>
                    <span className="text-xs font-medium text-gray-500">
                      Overdue
                    </span>
                    <span className="text-lg font-bold text-gray-800 mt-0.5">
                      {maintenanceStats?.overdueCount || 0}
                    </span>
                    {maintenanceStats?.overdueCount ? (
                      <div className="flex items-center gap-1 mt-1">
                        <div
                          className={cn(
                            'h-1 bg-red-100 rounded-full flex-grow',
                            totalMaintenanceLogs > 0 &&
                              'relative overflow-hidden',
                          )}
                        >
                          {totalMaintenanceLogs > 0 && (
                            <div
                              className="absolute inset-y-0 bg-red-500 rounded-full"
                              style={{
                                width: `${(maintenanceStats.overdueCount / totalMaintenanceLogs) * 100}%`,
                              }}
                            ></div>
                          )}
                        </div>
                      </div>
                    ) : null}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Maintenance Logs - Enhanced */}
        <Card className="overflow-hidden border border-gray-100 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 bg-gradient-to-br from-gray-50/80 to-white">
          <CardHeader className="pb-2 border-b border-gray-100">
            <CardTitle className="text-base font-medium flex items-center gap-2 text-slate-800">
              <ClipboardCheck className="h-4 w-4 text-blue-600" />
              Recent Maintenance
            </CardTitle>
          </CardHeader>
          <CardContent className="p-5">
            {isLoading ? (
              <div className="h-40 flex items-center justify-center">
                <div className="flex flex-col items-center">
                  <div className="animate-spin w-8 h-8 border-3 border-blue-200 border-t-blue-600 rounded-full mb-2"></div>
                  <p className="text-sm text-muted-foreground">
                    Loading recent maintenance logs...
                  </p>
                </div>
              </div>
            ) : maintenanceStats?.recent &&
              maintenanceStats.recent.length > 0 ? (
              <div className="divide-y divide-gray-100">
                {maintenanceStats.recent.map((log) => (
                  <div
                    key={log.id}
                    className="flex items-center justify-between py-3 px-2 hover:bg-blue-50/30 rounded-lg transition-colors group cursor-pointer"
                  >
                    <div className="flex items-start gap-3">
                      <div
                        className={cn(
                          'rounded-full p-2.5 shadow-sm transition-transform group-hover:scale-110',
                          log.status === 'completed'
                            ? 'bg-emerald-100'
                            : log.status === 'overdue'
                              ? 'bg-red-100'
                              : 'bg-amber-100',
                        )}
                      >
                        <ClipboardList
                          className={cn(
                            'h-5 w-5',
                            log.status === 'completed'
                              ? 'text-emerald-600'
                              : log.status === 'overdue'
                                ? 'text-red-600'
                                : 'text-amber-600',
                          )}
                        />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 group-hover:text-blue-700 transition-colors">
                          {log.title}
                        </p>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <span className="text-xs">{log.id}</span>
                          <span className="text-xs text-gray-300">•</span>
                          <div className="flex items-center gap-1">
                            <Tag className="h-3 w-3" />
                            <span className="text-xs">{log.type}</span>
                          </div>
                          <span className="text-xs text-gray-300">•</span>
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span className="text-xs">{log.date}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <Badge
                      variant={
                        log.status === 'completed'
                          ? 'success'
                          : log.status === 'overdue'
                            ? 'destructive'
                            : 'warning'
                      }
                      className="shadow-sm"
                    >
                      {log.status === 'completed'
                        ? 'Completed'
                        : log.status === 'in_progress'
                          ? 'In Progress'
                          : log.status === 'pending'
                            ? 'Pending'
                            : log.status === 'overdue'
                              ? 'Overdue'
                              : log.status}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-10 px-6 text-center space-y-4">
                <div className="relative">
                  <ClipboardList className="h-16 w-16 text-gray-200" />
                  <AlertTriangle className="h-6 w-6 text-blue-500 absolute bottom-0 right-0" />
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-1">
                    No Maintenance Logs Found
                  </h3>
                  <p className="text-xs text-gray-500 mb-3">
                    Get started by creating your first maintenance log.
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-200 text-blue-600 hover:bg-blue-50"
                    onClick={() => router.push('/maintenance-logs/create')}
                  >
                    Add Maintenance Log
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
