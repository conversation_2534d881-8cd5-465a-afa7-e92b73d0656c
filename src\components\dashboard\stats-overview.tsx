'use client';

import { Card, CardContent } from '@/components/ui/card';
import {
  useComplaintsStats,
  useMaintenanceLogsStats,
  usePMACertificatesStats,
} from '@/features/dashboard/hooks';
import { Clock, MessagesSquare, CheckCircle2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useEffect, useState } from 'react';

interface StatCardProps {
  title: string;
  value: string | number;
  description: string;
  icon: React.ReactNode;
  iconColor: string;
  bgGradient: string;
  onClick?: () => void;
}

function StatCard({
  title,
  value,
  description,
  icon,
  iconColor,
  bgGradient,
  onClick,
}: StatCardProps) {
  const [animatedValue, setAnimatedValue] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  // Animation for counting up numbers
  useEffect(() => {
    setIsVisible(true);

    // If value is a ratio like "5/10", we only animate the first number
    if (typeof value === 'string' && value.includes('/')) {
      const firstNumber = parseInt(value.split('/')[0], 10);
      if (!isNaN(firstNumber)) {
        const duration = 1000; // 1 second
        const steps = 20;
        const increment = firstNumber / steps;
        let current = 0;

        const timer = setInterval(() => {
          current += increment;
          if (current >= firstNumber) {
            setAnimatedValue(firstNumber);
            clearInterval(timer);
          } else {
            setAnimatedValue(Math.floor(current));
          }
        }, duration / steps);

        return () => clearInterval(timer);
      }
      return;
    }

    // Handle regular numbers
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;
    if (typeof numericValue === 'number' && !isNaN(numericValue)) {
      const duration = 1000; // 1 second
      const steps = 20;
      const increment = numericValue / steps;
      let current = 0;

      const timer = setInterval(() => {
        current += increment;
        if (current >= numericValue) {
          setAnimatedValue(numericValue);
          clearInterval(timer);
        } else {
          setAnimatedValue(Math.floor(current));
        }
      }, duration / steps);

      return () => clearInterval(timer);
    }
  }, [value]);

  // Format the display value
  const displayValue = (() => {
    if (typeof value === 'string' && value.includes('/')) {
      const [_, second] = value.split('/');
      return `${animatedValue}/${second}`;
    }
    return animatedValue;
  })();

  return (
    <Card
      className={cn(
        'overflow-hidden border-none shadow-md hover:shadow-lg transition-all duration-300',
        'relative group cursor-pointer animate-in fade-in duration-500',
        bgGradient,
        onClick ? 'hover:-translate-y-1' : '',
        isVisible ? 'opacity-100' : 'opacity-0',
      )}
      onClick={onClick}
    >
      <div className="absolute inset-0 bg-white/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
              {title}
            </p>
            <div className="flex items-baseline gap-2">
              <p className="text-3xl font-bold tracking-tight">
                {displayValue}
              </p>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {description}
            </p>
          </div>

          <div
            className={cn(
              'rounded-full p-3 w-12 h-12 flex items-center justify-center shrink-0',
              iconColor,
              'shadow-sm group-hover:scale-110 transition-transform duration-300',
            )}
          >
            {icon}
          </div>
        </div>
      </CardContent>

      <div
        className={cn(
          'absolute bottom-0 left-0 right-0 h-1',
          iconColor.replace('bg-', 'bg-').replace('/10', ''),
        )}
        style={{ opacity: 0.3 }}
      />
    </Card>
  );
}

export function StatsOverview() {
  const { data: pmaStats } = usePMACertificatesStats();
  const { data: maintenanceStats } = useMaintenanceLogsStats();
  const { data: complaintsStats } = useComplaintsStats();
  const router = useRouter();

  // Use real data or fallback to zeros
  const expiringPMAs = pmaStats?.expiringSoonCount || 0;
  const _overdueMaintenance = maintenanceStats?.overdueCount || 0;
  const openComplaints = complaintsStats?.openCount || 0;

  return (
    <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
      <StatCard
        title="Expiring Soon"
        value={expiringPMAs}
        description="Certificates expiring within the next 30 days"
        icon={<Clock className="h-5 w-5 text-amber-600" />}
        iconColor="bg-amber-100"
        bgGradient="bg-gradient-to-br from-amber-50 to-amber-100/50"
        onClick={() => router.push('/pmas')}
      />

      {/* <StatCard
        title="Overdue Tasks"
        value={overdueMaintenance}
        description="Maintenance tasks completed after 5 PM"
        icon={<AlertTriangle className="h-5 w-5 text-red-600" />}
        iconColor="bg-red-100"
        bgGradient="bg-gradient-to-br from-red-50 to-red-100/50"
        onClick={() => router.push('/maintenance-logs')}
      /> */}

      <StatCard
        title="Daily Completion"
        value={`${maintenanceStats?.completedCount || 0}/${pmaStats?.total || 0}`}
        description="Completed maintenance logs vs total certificates"
        icon={<CheckCircle2 className="h-5 w-5 text-emerald-600" />}
        iconColor="bg-emerald-100"
        bgGradient="bg-gradient-to-br from-emerald-50 to-emerald-100/50"
        onClick={() => router.push('/maintenance-logs')}
      />

      <StatCard
        title="Open Complaints"
        value={openComplaints}
        description="Complaints requiring attention"
        icon={<MessagesSquare className="h-5 w-5 text-blue-600" />}
        iconColor="bg-blue-100"
        bgGradient="bg-gradient-to-br from-blue-50 to-blue-100/50"
        onClick={() => router.push('/complaints')}
      />
    </div>
  );
}
