'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RotateCcw, Search } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

interface FilterState {
  status: string;
  pmaNo: string;
  contractor: string;
  dateFrom: string;
  dateTo: string;
}

export function AdminComplaintFilters() {
  const t = useTranslations('complaints');
  const [filters, setFilters] = useState<FilterState>({
    status: '',
    pmaNo: '',
    contractor: '',
    dateFrom: '',
    dateTo: '',
  });

  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleReset = () => {
    setFilters({
      status: '',
      pmaNo: '',
      contractor: '',
      dateFrom: '',
      dateTo: '',
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Search className="h-5 w-5" />
          {t('filters.title')}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Status Filter */}
          <div className="space-y-2">
            <Label htmlFor="status">{t('filters.status')}</Label>
            <Select 
              value={filters.status} 
              onValueChange={(value) => handleFilterChange('status', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('filters.statusPlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('filters.allStatus')}</SelectItem>
                <SelectItem value="open">{t('status.open')}</SelectItem>
                <SelectItem value="inProgress">{t('status.inProgress')}</SelectItem>
                <SelectItem value="closed">{t('status.closed')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* PMA Number Filter */}
          <div className="space-y-2">
            <Label htmlFor="pmaNo">{t('filters.pmaNo')}</Label>
            <Input
              id="pmaNo"
              value={filters.pmaNo}
              onChange={(e) => handleFilterChange('pmaNo', e.target.value)}
              placeholder={t('filters.pmaNoPlaceholder')}
            />
          </div>

          {/* Contractor Filter */}
          <div className="space-y-2">
            <Label htmlFor="contractor">{t('admin.filters.contractor')}</Label>
            <Select 
              value={filters.contractor} 
              onValueChange={(value) => handleFilterChange('contractor', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('admin.filters.contractorPlaceholder')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('admin.filters.allContractors')}</SelectItem>
                <SelectItem value="contractor1">ABC Contractor</SelectItem>
                <SelectItem value="contractor2">XYZ Engineering</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Date From */}
          <div className="space-y-2">
            <Label htmlFor="dateFrom">{t('admin.filters.dateFrom')}</Label>
            <Input
              id="dateFrom"
              type="date"
              value={filters.dateFrom}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
            />
          </div>

          {/* Date To */}
          <div className="space-y-2">
            <Label htmlFor="dateTo">{t('admin.filters.dateTo')}</Label>
            <Input
              id="dateTo"
              type="date"
              value={filters.dateTo}
              onChange={(e) => handleFilterChange('dateTo', e.target.value)}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 mt-4">
          <Button variant="outline" onClick={handleReset} className="flex items-center gap-2">
            <RotateCcw className="h-4 w-4" />
            {t('filters.resetFilters')}
          </Button>
          <Button className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            {t('common.search')}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
