'use client';

import { useTranslations } from 'next-intl';
import { AdminComplaintFilters } from './admin-complaint-filters';
import { AdminComplaintPendingItems } from './admin-complaint-pending-items';
import { AdminComplaintStats } from './admin-complaint-stats';
import { AdminComplaintStatusChart } from './admin-complaint-status-chart';
import { AdminComplaintTable } from './admin-complaint-table';
import { AdminComplaintTrend } from './admin-complaint-trend';

export function AdminComplaintLogDashboard() {
  const t = useTranslations();

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex flex-col gap-2">
        <h1 className="text-2xl font-bold text-gray-900">
          {t('navigation.admin_complaint_log')}
        </h1>
        <p className="text-sm text-gray-600">
          {t('complaints.admin.subtitle')}
        </p>
      </div>

      {/* Stats Cards */}
      <AdminComplaintStats />

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Trend Chart */}
        <AdminComplaintTrend />
        
        {/* Status Chart */}
        <AdminComplaintStatusChart />
      </div>

      {/* Filters and Actions */}
      <AdminComplaintFilters />

      {/* Pending Items */}
      <AdminComplaintPendingItems />

      {/* Main Table */}
      <AdminComplaintTable />
    </div>
  );
}
