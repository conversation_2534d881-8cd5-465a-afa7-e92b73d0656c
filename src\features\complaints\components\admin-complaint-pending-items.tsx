'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, CheckCircle, Clock, Eye } from 'lucide-react';
import { useTranslations } from 'next-intl';

// Mock data for pending items
const pendingItems = [
  {
    id: 'RTC-2024-001',
    pmaNo: 'PMA-GL-001',
    location: 'Hospital Seberang Jaya',
    status: 'immediate_action',
    priority: 'high',
    daysOverdue: 5,
    type: 'maintenance',
  },
  {
    id: 'RTC-2024-002',
    pmaNo: 'PMA-GL-009',
    location: 'Hospital Sultanah Bahiyah',
    status: 'inspection_required',
    priority: 'medium',
    daysOverdue: 2,
    type: 'inspection',
  },
  {
    id: 'RTC-2024-003',
    pmaNo: 'PMA-GL-043',
    location: 'PEJABAT KESIHATAN DAERAH UTARA SEBERANG PERAI',
    status: 'maintenance_due',
    priority: 'low',
    daysOverdue: 0,
    type: 'preventive',
  },
];

export function AdminComplaintPendingItems() {
  const t = useTranslations('complaints');

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'medium':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      high: 'destructive',
      medium: 'secondary',
      low: 'outline',
    } as const;
    
    return (
      <Badge variant={variants[priority as keyof typeof variants] || 'outline'}>
        {priority}
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      immediate_action: { variant: 'destructive' as const, text: 'Immediate Action' },
      inspection_required: { variant: 'secondary' as const, text: 'Inspection Required' },
      maintenance_due: { variant: 'outline' as const, text: 'Maintenance Due' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'outline' as const, text: status };
    
    return (
      <Badge variant={config.variant}>
        {config.text}
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">
            {t('admin.pendingItems.title')}
          </CardTitle>
          <Badge variant="secondary" className="bg-red-100 text-red-800">
            {pendingItems.length} {t('admin.pendingItems.itemsRemaining')}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {pendingItems.map((item) => (
            <div
              key={item.id}
              className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-4">
                {getPriorityIcon(item.priority)}
                <div className="flex flex-col gap-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-blue-600">{item.id}</span>
                    <span className="text-sm text-gray-500">{item.pmaNo}</span>
                  </div>
                  <p className="text-sm text-gray-700">{item.location}</p>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(item.status)}
                    {getPriorityBadge(item.priority)}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                {item.daysOverdue > 0 && (
                  <span className="text-sm text-red-600 font-medium">
                    {item.daysOverdue} {t('admin.pendingItems.daysOverdue')}
                  </span>
                )}
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  {t('common.viewDetails')}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
