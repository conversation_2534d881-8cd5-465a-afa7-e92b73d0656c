'use client';

import { Card, CardContent } from '@/components/ui/card';
import { AlertTriangle, CheckCircle, Clock, FileText } from 'lucide-react';
import { useTranslations } from 'next-intl';

export function AdminComplaintStats() {
  const t = useTranslations('complaints');
  
  // Mock data for now
  const stats = {
    total: 156,
    pending: 23,
    inProgress: 89,
    completed: 44,
  };

  const statCards = [
    {
      title: t('admin.stats.totalComplaints'),
      value: stats?.total || 156,
      icon: FileText,
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-600',
      borderColor: 'border-blue-200',
    },
    {
      title: t('admin.stats.pendingReview'),
      value: stats?.pending || 23,
      icon: Clock,
      bgColor: 'bg-orange-50',
      iconColor: 'text-orange-600',
      borderColor: 'border-orange-200',
    },
    {
      title: t('admin.stats.inProgress'),
      value: stats?.inProgress || 89,
      icon: AlertTriangle,
      bgColor: 'bg-yellow-50',
      iconColor: 'text-yellow-600',
      borderColor: 'border-yellow-200',
    },
    {
      title: t('admin.stats.completed'),
      value: stats?.completed || 44,
      icon: CheckCircle,
      bgColor: 'bg-green-50',
      iconColor: 'text-green-600',
      borderColor: 'border-green-200',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {statCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index} className={`${stat.borderColor} border-l-4`}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex flex-col gap-2">
                  <p className="text-sm font-medium text-gray-600">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-gray-900">
                    {stat.value}
                  </p>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor}`}>
                  <Icon className={`h-6 w-6 ${stat.iconColor}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
