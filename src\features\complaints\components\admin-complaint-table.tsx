'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { Download, Eye, Filter, Plus } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

// Mock data for the complaint log table
const mockComplaints = [
  {
    id: 'RTC-2024-001',
    dateSubmitted: '14/01/2024',
    pmaNo: 'PMA-GL-001',
    location: 'Hospital Seberang Jaya',
    contractor: 'ABC Engineering',
    status: 'open',
    followUp: 'in_progress',
    completionDate: '-',
    cost: 'RM 1,500.00',
  },
  {
    id: 'RTC-2024-002',
    dateSubmitted: '12/01/2024',
    pmaNo: 'PMA-GL-009',
    location: 'Hospital Sultanah Bahiyah',
    contractor: 'XYZ Maintenance',
    status: 'closed',
    followUp: 'verified',
    completionDate: '15/01/2024',
    cost: 'RM 2,300.00',
  },
  {
    id: 'RTC-2024-003',
    dateSubmitted: '10/01/2024',
    pmaNo: 'PMA-GL-043',
    location: 'Hospital Pulau Pinang',
    contractor: 'DEF Solutions',
    status: 'in_progress',
    followUp: 'pending_approval',
    completionDate: '-',
    cost: 'RM 800.00',
  },
  {
    id: 'RTC-2024-004',
    dateSubmitted: '08/01/2024',
    pmaNo: 'PMA-GL-038',
    location: 'Plaza Rakyat, Lift 2',
    contractor: 'GHI Engineering',
    status: 'closed',
    followUp: 'verified',
    completionDate: '12/01/2024',
    cost: 'RM 450.00',
  },
  {
    id: 'RTC-2024-005',
    dateSubmitted: '05/01/2024',
    pmaNo: 'PMA-GL-063',
    location: 'Plaza Rakyat, Lift 4',
    contractor: 'JKL Maintenance',
    status: 'open',
    followUp: 'in_progress',
    completionDate: '-',
    cost: 'RM 1,200.00',
  },
];

export function AdminComplaintTable() {
  const t = useTranslations('complaints');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      open: { variant: 'secondary' as const, color: 'bg-blue-100 text-blue-800' },
      in_progress: { variant: 'secondary' as const, color: 'bg-yellow-100 text-yellow-800' },
      closed: { variant: 'secondary' as const, color: 'bg-green-100 text-green-800' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'secondary' as const, color: 'bg-gray-100 text-gray-800' };
    
    return (
      <Badge variant={config.variant} className={config.color}>
        {t(`status.${status}`)}
      </Badge>
    );
  };

  const getFollowUpBadge = (followUp: string) => {
    const followUpConfig = {
      in_progress: { variant: 'outline' as const, color: 'border-blue-200 text-blue-700' },
      pending_approval: { variant: 'outline' as const, color: 'border-orange-200 text-orange-700' },
      verified: { variant: 'outline' as const, color: 'border-green-200 text-green-700' },
    };
    
    const config = followUpConfig[followUp as keyof typeof followUpConfig] || { variant: 'outline' as const, color: 'border-gray-200 text-gray-700' };
    
    return (
      <Badge variant={config.variant} className={config.color}>
        {t(`followUp.${followUp}`)}
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold">
              {t('admin.table.title')}
            </CardTitle>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              {t('admin.table.export')}
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              {t('admin.table.advancedFilter')}
            </Button>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              {t('admin.table.addComplaint')}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('table.reportId')}</TableHead>
                <TableHead>{t('table.dateSubmitted')}</TableHead>
                <TableHead>{t('table.pmaNumber')}</TableHead>
                <TableHead>{t('table.location')}</TableHead>
                <TableHead>{t('admin.table.contractor')}</TableHead>
                <TableHead>{t('table.status')}</TableHead>
                <TableHead>{t('table.followUp')}</TableHead>
                <TableHead>{t('table.completionDate')}</TableHead>
                <TableHead>{t('table.cost')}</TableHead>
                <TableHead>{t('table.actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockComplaints.map((complaint) => (
                <TableRow key={complaint.id}>
                  <TableCell className="font-medium text-blue-600">
                    {complaint.id}
                  </TableCell>
                  <TableCell>{complaint.dateSubmitted}</TableCell>
                  <TableCell>{complaint.pmaNo}</TableCell>
                  <TableCell className="max-w-xs truncate">
                    {complaint.location}
                  </TableCell>
                  <TableCell>{complaint.contractor}</TableCell>
                  <TableCell>
                    {getStatusBadge(complaint.status)}
                  </TableCell>
                  <TableCell>
                    {getFollowUpBadge(complaint.followUp)}
                  </TableCell>
                  <TableCell>{complaint.completionDate}</TableCell>
                  <TableCell>{complaint.cost}</TableCell>
                  <TableCell>
                    <Button variant="outline" size="sm" className="flex items-center gap-1">
                      <Eye className="h-4 w-4" />
                      {t('common.viewDetails')}
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-4">
          <p className="text-sm text-gray-700">
            {t('pagination.showing', {
              start: 1,
              end: Math.min(itemsPerPage, mockComplaints.length),
              total: 156,
            })}
          </p>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled={currentPage === 1}>
              {t('pagination.previous')}
            </Button>
            <div className="flex items-center gap-1">
              {[1, 2, 3, '...', 16].map((page, index) => (
                <Button
                  key={index}
                  variant={page === currentPage ? 'default' : 'outline'}
                  size="sm"
                  className="w-8 h-8"
                  disabled={page === '...'}
                >
                  {page}
                </Button>
              ))}
            </div>
            <Button variant="outline" size="sm">
              {t('pagination.next')}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
