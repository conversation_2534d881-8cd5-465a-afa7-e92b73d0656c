'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { useTranslations } from 'next-intl';
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, ResponsiveContainer, XAxis, YAxis } from 'recharts';

// Mock data based on the image provided
const trendData = [
  { name: '7 Jan', value: 120 },
  { name: '7 Jan', value: 180 },
  { name: '14 Jan', value: 170 },
  { name: '21 Jan', value: 210 },
  { name: '28 Jan', value: 250 },
];

export function AdminComplaintTrend() {
  const t = useTranslations('complaints');

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">
          {t('admin.charts.trendTitle')}
        </CardTitle>
        <p className="text-sm text-gray-600">
          {t('admin.charts.trendSubtitle')}
        </p>
      </CardHeader>
      <CardContent>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <Bar<PERSON>hart data={trendData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="name" 
                tick={{ fontSize: 12 }}
                tickLine={{ stroke: '#e5e7eb' }}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                tickLine={{ stroke: '#e5e7eb' }}
              />
              <Bar 
                dataKey="value" 
                fill="#3b82f6"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
