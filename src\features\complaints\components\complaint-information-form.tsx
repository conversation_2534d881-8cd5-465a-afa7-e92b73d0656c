'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAgencyTranslations } from '@/hooks/use-translations';
import { AGENCY_CODES } from '@/lib/constants';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useCreateComplaint } from '../hooks/use-complaints-simple';
import { CreateComplaintInput, createComplaintInputSchema } from '../schemas';

interface ComplaintInformationFormProps {
  onSuccess: () => void;
  onCancel: () => void;
}

export function ComplaintInformationForm({
  onSuccess,
  onCancel,
}: ComplaintInformationFormProps) {
  const [beforePhotos, setBeforePhotos] = useState<File[]>([]);
  const createComplaintMutation = useCreateComplaint();
  const tAgency = useAgencyTranslations();
  const t = useTranslations('complaints.form');
  const form = useForm<CreateComplaintInput>({
    resolver: zodResolver(createComplaintInputSchema),
    defaultValues: {
      email: '',
      date: new Date(),
      expected_completion_date: new Date(),
      contractor_name: '',
      location: '',
      description: '',
      involves_mantrap: false,
      no_pma_lif: '',
      actual_completion_date: undefined,
      status: 'open',
      repair_cost: 0,
    },
  });

  const handleBeforePhotoUpload = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter((file) => {
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
      const isValidType = ['image/jpeg', 'image/png', 'image/jpg'].includes(
        file.type,
      );
      return isValidSize && isValidType;
    });
    setBeforePhotos((prev) => [...prev, ...validFiles]);
  };

  const removeBeforePhoto = (index: number) => {
    setBeforePhotos((prev) => prev.filter((_, i) => i !== index));
  };
  const onSubmit = async (data: CreateComplaintInput) => {
    try {
      const result = await createComplaintMutation.mutateAsync({
        ...data,
        proofOfRepairFiles: beforePhotos,
      });

      // Show success message with ticket ID
      if (result.number) {
        console.log(
          `Complaint created successfully! Ticket ID: ${result.number}`,
        );
      }

      onSuccess();
    } catch (error) {
      console.error('Failed to create complaint:', error);
    }
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-6 max-w-5xl">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('title')}</h1>
        <p className="text-gray-600">{t('subtitle')}</p>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* A. Complaint Information */}
        <Card className="shadow-sm">
          <CardHeader className="pb-4 bg-blue-50">
            <CardTitle className="text-lg font-semibold text-blue-800 flex items-center gap-2">
              {t('sectionA.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 p-4 sm:p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 lg:gap-6">
              <div className="space-y-2">
                <Label
                  htmlFor="email"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionA.email')}
                </Label>
                <Input
                  id="email"
                  type="email"
                  {...form.register('email')}
                  className={cn(
                    'h-10',
                    form.formState.errors.email &&
                      'border-red-500 focus-visible:ring-red-500',
                  )}
                  placeholder={t('placeholders.email')}
                />
                {form.formState.errors.email && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.email.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="contactNumber"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionA.name')}
                </Label>{' '}
                <Input
                  id="contactNumber"
                  {...form.register('contractor_name')}
                  className="h-10"
                  placeholder="Ahmad Zainuddin"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionA.complaintDate')}
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full h-10 justify-start text-left font-normal',
                        !form.watch('date') && 'text-muted-foreground',
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {form.watch('date') ? (
                        format(form.watch('date'), 'yyyy-MM-dd')
                      ) : (
                        <span>{t('placeholders.selectDate')}</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={form.watch('date')}
                      onSelect={(date) =>
                        form.setValue('date', date || new Date())
                      }
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionA.agency')}
                </Label>{' '}
                <Select
                  onValueChange={(value) => {
                    // For now, we'll store it as contractor_name since agency is not in schema
                    form.setValue('contractor_name', value);
                  }}
                >
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder={t('placeholders.selectAgency')} />
                  </SelectTrigger>
                  <SelectContent>
                    {AGENCY_CODES.map((agencyCode) => (
                      <SelectItem key={agencyCode} value={agencyCode}>
                        {tAgency(agencyCode)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="contractorCompanyName"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionA.contractorCompanyName')}
                </Label>{' '}
                <Input
                  id="contractorCompanyName"
                  {...form.register('contractor_name')}
                  className="h-10"
                  placeholder="Syarikat Lif Teknologi Sdn Bhd"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="location"
                className="text-sm font-medium text-gray-700"
              >
                {t('sectionA.location')}
              </Label>
              <Input
                id="location"
                {...form.register('location')}
                className="h-10"
                placeholder=""
              />
            </div>

            <div className="space-y-2">
              <Label
                htmlFor="noPmaLif"
                className="text-sm font-medium text-gray-700"
              >
                {t('sectionA.pmaNumber')}
              </Label>{' '}
              <Input
                id="noPmaLif"
                {...form.register('no_pma_lif')}
                className="h-10"
                placeholder=""
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                {t('sectionA.damageDescription')}
              </Label>
              <Input
                {...form.register('description')}
                className="h-10"
                placeholder=""
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                {t('sectionA.expectedCompletionDate')}
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full h-10 justify-start text-left font-normal',
                      !form.watch('expected_completion_date') &&
                        'text-muted-foreground',
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {form.watch('expected_completion_date') ? (
                      format(
                        form.watch('expected_completion_date'),
                        'yyyy-MM-dd',
                      )
                    ) : (
                      <span>{t('placeholders.selectDate')}</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={form.watch('expected_completion_date')}
                    onSelect={(date) =>
                      form.setValue(
                        'expected_completion_date',
                        date || new Date(),
                      )
                    }
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionA.involvesManTrap')}
                </Label>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="mantrap"
                      value="Ya"
                      className="w-4 h-4"
                    />
                    <span className="text-sm">{t('sectionA.yes')}</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="mantrap"
                      value="Tidak"
                      className="w-4 h-4"
                      defaultChecked
                    />
                    <span className="text-sm">{t('sectionA.no')}</span>
                  </label>
                </div>
              </div>

              {/* Before Photo Upload */}
              <div className="mt-4 space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionB.beforePhoto')}
                </Label>
                <div className="border-2 border-dashed border-blue-300 rounded-lg p-4 sm:p-6 text-center bg-blue-50">
                  <input
                    type="file"
                    id="before-upload"
                    multiple
                    accept="image/*"
                    onChange={handleBeforePhotoUpload}
                    className="hidden"
                  />
                  <div className="text-blue-600 mb-2">📷</div>
                  <div className="text-sm text-blue-600 font-medium mb-2">
                    {t('sectionB.beforePhoto')}
                  </div>
                  <label htmlFor="before-upload">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="bg-blue-600 text-white border-blue-600 hover:bg-blue-700"
                      asChild
                    >
                      <span className="cursor-pointer">
                        {t('actions.upload')}
                      </span>
                    </Button>
                  </label>
                  {beforePhotos.length > 0 && (
                    <div className="mt-2">
                      <div className="text-xs text-green-600 mb-2">
                        {beforePhotos.length} file(s) uploaded
                      </div>
                      <div className="flex flex-wrap gap-2 justify-center">
                        {beforePhotos.map((file, index) => (
                          <div
                            key={index}
                            className="bg-white p-2 rounded border text-xs flex items-center gap-2"
                          >
                            <span className="truncate max-w-20">
                              {file.name}
                            </span>
                            <button
                              type="button"
                              onClick={() => removeBeforePhoto(index)}
                              className="text-red-500 hover:text-red-700"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                <p className="text-xs text-gray-500 text-center">
                  {t('fileUpload.acceptedFormats')}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Important Note */}
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className="text-orange-600 mt-1">⚠️</div>
              <div className="text-sm">
                <div className="font-medium text-orange-800 mb-1">
                  {t('notes.title')}
                </div>
                <ul className="space-y-1 text-orange-700">
                  <li>• {t('notes.required')}</li>
                  <li>• {t('notes.proofRequired')}</li>
                  <li>• {t('notes.autoSubmit')}</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="w-full sm:w-auto px-6"
          >
            {t('buttons.back')}
          </Button>
          <Button
            type="submit"
            disabled={createComplaintMutation.isPending}
            className="w-full sm:w-auto px-8 bg-blue-600 hover:bg-blue-700"
          >
            {createComplaintMutation.isPending ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                {t('buttons.submitting')}
              </>
            ) : (
              <>{t('buttons.submit')}</>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
