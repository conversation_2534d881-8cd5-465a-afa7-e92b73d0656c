import { supabase } from '@/lib/supabase';
import { useQuery } from '@tanstack/react-query';

interface AdminComplaintStats {
  total: number;
  pending: number;
  inProgress: number;
  completed: number;
  open: number;
  closed: number;
}

export function useAdminComplaintStats() {
  return useQuery({
    queryKey: ['admin-complaint-stats'],
    queryFn: async (): Promise<AdminComplaintStats> => {
      const { data, error } = await supabase
        .from('complaints')
        .select('status, follow_up')
        .is('deleted_at', null);

      if (error) {
        throw new Error(`Failed to fetch complaint stats: ${error.message}`);
      }

      const stats = data.reduce(
        (acc, complaint) => {
          acc.total++;
          
          switch (complaint.status) {
            case 'open':
              acc.open++;
              acc.pending++;
              break;
            case 'on_hold':
              acc.inProgress++;
              break;
            case 'closed':
              acc.closed++;
              acc.completed++;
              break;
          }
          
          return acc;
        },
        {
          total: 0,
          pending: 0,
          inProgress: 0,
          completed: 0,
          open: 0,
          closed: 0,
        }
      );

      return stats;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
