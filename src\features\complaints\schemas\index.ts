/**
 * Zod schemas for complaints feature
 * Validates complaint forms and API requests
 */

import { z } from 'zod';

// Base damage complaint form schema
export const damageComplaintFormSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),

  damage_complaint_date: z
    .string()
    .min(1, 'Damage complaint date is required')
    .refine((date) => {
      const selectedDate = new Date(date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return selectedDate <= today;
    }, 'Damage complaint date cannot be in the future'),

  agency_id: z.string().optional(),

  contractor_company_name: z
    .string()
    .min(1, 'Company/Contractor name is required')
    .min(2, 'Company name must be at least 2 characters')
    .max(100, 'Company name must not exceed 100 characters'),

  location: z
    .string()
    .min(1, 'Location is required')
    .min(5, 'Location must be at least 5 characters')
    .max(200, 'Location must not exceed 200 characters'),

  no_pma_lif: z
    .string()
    .min(1, 'NO PMA LIF is required')
    .regex(/^WP\s+PMA\s+\d{4,}$/i, 'Invalid format. Example: WP PMA 1234'),

  damage_complaint_description: z
    .string()
    .min(1, 'Damage complaint description is required')
    .min(10, 'Description must be at least 10 characters')
    .max(1000, 'Description must not exceed 1000 characters'),

  expected_completion_date: z
    .string()
    .min(1, 'Expected completion date is required')
    .refine((date) => {
      const selectedDate = new Date(date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return selectedDate >= today;
    }, 'Expected completion date cannot be in the past'),

  involves_mantrap: z.boolean().default(false),

  // Optional completion fields
  actual_completion_date: z
    .string()
    .optional()
    .refine((date) => {
      if (!date) return true;
      const selectedDate = new Date(date);
      const today = new Date();
      today.setHours(23, 59, 59, 999);
      return selectedDate <= today;
    }, 'Actual completion date cannot be in the future'),

  repair_completion_time: z
    .string()
    .optional()
    .refine((time) => {
      if (!time) return true;
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
      return timeRegex.test(time);
    }, 'Invalid time format. Use HH:MM (e.g., 14:30)'),

  cause_of_damage: z
    .string()
    .optional()
    .refine((value) => {
      if (!value) return true;
      return value.length <= 500;
    }, 'Cause of damage must not exceed 500 characters'),

  correction_action: z
    .string()
    .optional()
    .refine((value) => {
      if (!value) return true;
      return value.length <= 500;
    }, 'Correction action must not exceed 500 characters'),

  proof_of_repair_files: z
    .array(z.instanceof(File))
    .optional()
    .refine((files) => {
      if (!files) return true;
      return files.length <= 5;
    }, 'Maximum 5 files allowed')
    .refine((files) => {
      if (!files) return true;
      return files.every((file) => file.size <= 10 * 1024 * 1024); // 10MB per file
    }, 'Each file must be smaller than 10MB')
    .refine((files) => {
      if (!files) return true;
      const allowedTypes = [
        'application/pdf',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp',
      ];
      return files.every((file) => allowedTypes.includes(file.type));
    }, 'Only PDF and image files (JPG, PNG, WebP) are allowed'),

  repair_cost_rm: z
    .number()
    .optional()
    .refine((value) => {
      if (value === undefined || value === null) return true;
      return value >= 0;
    }, 'Repair cost must be 0 or positive')
    .refine((value) => {
      if (value === undefined || value === null) return true;
      return value <= 1000000; // Max 1 million RM
    }, 'Repair cost cannot exceed RM 1,000,000'),
});

// Schema for updating complaint status
export const updateComplaintStatusSchema = z.object({
  status: z.enum([
    'open',
    'in_progress',
    'resolved',
    'pending_approval',
    'verified',
    'closed',
  ]),
  updated_by: z.string().uuid('Invalid user ID'),
});

// Schema for complaint filters
export const complaintFiltersSchema = z.object({
  status: z
    .array(
      z.enum([
        'open',
        'in_progress',
        'resolved',
        'pending_approval',
        'verified',
        'closed',
      ]),
    )
    .optional(),
  agency_id: z.array(z.string().uuid()).optional(),
  date_range: z
    .object({
      start: z.string().min(1),
      end: z.string().min(1),
    })
    .optional(),
  search: z.string().optional(),
  contractor_company: z.string().optional(),
});

// Schema for complaint sort
export const complaintSortSchema = z.object({
  column: z.enum([
    'created_at',
    'damage_complaint_date',
    'expected_completion_date',
    'status',
    'contractor_company_name',
    'location',
  ]),
  direction: z.enum(['asc', 'desc']),
});

// Schema for pagination
export const complaintPaginationSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10),
});

// Export inferred types
export type DamageComplaintFormValues = z.infer<
  typeof damageComplaintFormSchema
>;
export type UpdateComplaintStatusValues = z.infer<
  typeof updateComplaintStatusSchema
>;
export type ComplaintFiltersValues = z.infer<typeof complaintFiltersSchema>;
export type ComplaintSortValues = z.infer<typeof complaintSortSchema>;
export type ComplaintPaginationValues = z.infer<
  typeof complaintPaginationSchema
>;

// Helper function to create validation error messages
export const getValidationErrorMessage = (error: z.ZodError): string => {
  return error.errors[0]?.message || 'Validation error occurred';
};

// Schema for complaint creation with file URLs (after upload)
export const createComplaintSchema = damageComplaintFormSchema
  .omit({
    proof_of_repair_files: true,
  })
  .extend({
    proof_of_repair_urls: z.array(z.string().url()).optional(),
    created_by: z.string().uuid('Invalid user ID'),
  });

// Schema aligned with database structure for form components
export const createComplaintInputSchema = z.object({
  // Section A: Basic Information (maps to database fields)
  email: z.string().email('Please enter a valid email address'),
  date: z.date({
    required_error: 'Damage complaint date is required',
  }),
  expected_completion_date: z.date({
    required_error: 'Expected completion date is required',
  }),
  contractor_name: z.string().min(1, 'Contractor/Company name is required'),
  location: z.string().min(1, 'Location is required'),
  no_pma_lif: z.string().min(1, 'NO PMA LIF is required'),
  description: z.string().min(1, 'Description is required'),
  involves_mantrap: z.boolean().default(false),

  // Section B: Repair Information (maps to database fields)
  actual_completion_date: z.date().optional(),
  repair_completion_time: z.string().optional(),
  cause_of_damage: z.string().optional(),
  correction_action: z.string().optional(),
  proof_of_repair_urls: z.array(z.string().url()).optional(),
  repair_cost: z.number().optional(),

  // Status management
  status: z.enum(['open', 'on_hold', 'closed']).default('open'),
  follow_up: z
    .enum(['in_progress', 'pending_approval', 'verified'])
    .default('in_progress'),

  // File uploads (for form handling only)
  proofOfRepairFiles: z.array(z.instanceof(File)).optional(),
});

// Schema specifically for Section B updates (repair information only)
export const updateRepairInformationSchema = z.object({
  // Section A: Basic Information (optional for updates, but needed for form validation)
  email: z.string().email('Please enter a valid email address').optional(),
  date: z.date().optional(),
  expected_completion_date: z.date().optional(),
  contractor_name: z.string().optional(),
  location: z.string().optional(),
  no_pma_lif: z.string().optional(),
  description: z.string().optional(),
  involves_mantrap: z.boolean().default(false),

  // Section B: Repair Information (required for Section B updates)
  actual_completion_date: z.date({
    required_error: 'Actual completion date is required',
  }),
  repair_completion_time: z
    .string()
    .min(1, 'Repair completion time is required'),
  cause_of_damage: z.string().min(1, 'Cause of damage is required'),
  correction_action: z.string().min(1, 'Correction action is required'),
  proof_of_repair_urls: z.array(z.string()).optional(), // Allow any string for updates (URLs or file names)
  repair_cost: z
    .number()
    .min(0, 'Repair cost must be 0 or positive')
    .optional(),

  // Status management
  status: z.enum(['open', 'on_hold', 'closed']).default('closed'),
  follow_up: z
    .enum(['in_progress', 'pending_approval', 'verified'])
    .default('pending_approval'),

  // File uploads (for form handling only)
  proofOfRepairFiles: z.array(z.instanceof(File)).optional(),
});

export type CreateComplaintValues = z.infer<typeof createComplaintSchema>;

// For form components - same as DamageComplaintFormValues but with proper naming
export type CreateComplaintInput = z.infer<typeof createComplaintInputSchema>;

// For Section B repair information updates
export type UpdateRepairInformationInput = z.infer<
  typeof updateRepairInformationSchema
>;
