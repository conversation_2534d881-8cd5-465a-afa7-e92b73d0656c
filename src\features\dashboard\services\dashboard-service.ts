'use client';

import { supabase } from '@/lib/supabase';
import { addDays, format, subDays } from 'date-fns';

// Type definitions for the return types
export interface PMACertificateStats {
  statusDistribution: Array<{ name: string; value: number; color: string }>;
  monthlyTrend: Array<{ month: string; issued: number; renewed: number }>;
  categories: Array<{ name: string; count: number }>;
  problematicLifts: Array<{
    location: string;
    cost: number;
    hours: number;
    incidents: number;
  }>;
  recent: Array<{
    id: string;
    name: string;
    status: string;
    expiryDate: string | null;
  }>;
  total: number;
  validCount: number;
  expiredCount: number;
  expiringSoonCount: number;
}

export interface MaintenanceLogStats {
  statusDistribution: Array<{ name: string; value: number; color: string }>;
  byType: Array<{ name: string; count: number }>;
  monthlyTrend: Array<{
    month: string;
    preventive: number;
    corrective: number;
    emergency: number;
  }>;
  recent: Array<{
    id: string;
    title: string;
    type: string;
    status: string;
    date: string | null;
  }>;
  total: number;
  completedCount: number;
  pendingCount: number;
  overdueCount: number;
  completedMaintenance: number; // PMA certs with maintenance log for today (before 5pm)
  totalMaintenance: number; // Total PMA certificates count
  overdueMaintenance: number; // Maintenance logs done after 5pm
}

export interface ComplaintStats {
  statusDistribution: Array<{ name: string; value: number; color: string }>;
  byCategory: Array<{ name: string; count: number }>;
  monthlyTrend: Array<{ month: string; received: number; resolved: number }>;
  responseTime: Array<{ category: string; avgTime: number }>;
  recent: Array<{
    id: string;
    title: string;
    category: string;
    status: string;
    date: string | null;
  }>;
  trendAnalysis: Array<{ month: string; total: number }>;
  trendGrowthRate: number;
  total: number;
  newCount: number;
  inProgressCount: number;
  resolvedCount: number;
  closedCount: number;
  openCount: number;
  resolutionRate: number;
}

export interface SafetyIncidentStats {
  last30Days: number;
  trend: Array<{ month: string; count: number }>;
}

export interface ComplianceScore {
  score: number;
  pmaCompliance: number;
  maintenanceCompliance: number;
}

/**
 * Dashboard service for retrieving data for the dashboard
 */
export const DashboardService = {
  /**
   * Get PMA certificates stats
   * @param projectId Optional project ID to filter by
   * @returns PMA certificates stats
   */
  async getPMACertificatesStats(
    projectId?: string,
  ): Promise<PMACertificateStats> {
    try {
      // Get current date
      const now = new Date();

      // Define the shape of the certificates data we expect
      interface CertificateData {
        id: string;
        expiry_date?: string;
        status?: string;
        pma_number?: string;
        created_at?: string;
        updated_at?: string;
        state?: string;
        location?: string;
      }

      // Base query builder
      let query = supabase
        .from('pma_certificates')
        .select('id, expiry_date, status, pma_number, state, location');

      // Add project filter if provided
      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      // Execute query
      const { data, error } = await query;

      if (error) {
        console.error('Error fetching PMA certificates:', error);
        throw error;
      }

      // Fetch problematic lifts data
      const { data: maintenanceData, error: maintenanceError } = await supabase
        .from('maintenance_logs')
        .select(
          'id, pma_id, log_date, operation_log_type, description, created_at',
        )
        .order('created_at', { ascending: false });

      if (maintenanceError) {
        console.error('Error fetching maintenance data:', maintenanceError);
        throw maintenanceError;
      }

      // Cast data to expected shape
      const certificates = (data || []) as unknown as CertificateData[];

      // Calculate statistics based on expiry date only
      const valid = certificates.filter(
        (cert) =>
          cert.expiry_date && new Date(cert.expiry_date) > addDays(now, 30),
      ).length;

      const expiringSoon = certificates.filter(
        (cert) =>
          cert.expiry_date &&
          new Date(cert.expiry_date) <= addDays(now, 30) &&
          new Date(cert.expiry_date) >= now,
      ).length;

      const expired = certificates.filter(
        (cert) => cert.expiry_date && new Date(cert.expiry_date) < now,
      ).length;

      // Get monthly issuance data
      const sixMonthsAgo = subDays(now, 180);
      const { data: monthlyData, error: monthlyError } = await supabase
        .from('pma_certificates')
        .select('id, created_at, updated_at')
        .gte('created_at', format(sixMonthsAgo, 'yyyy-MM-dd'));

      if (monthlyError) {
        console.error('Error fetching monthly PMA data:', monthlyError);
        throw monthlyError;
      }

      // Cast monthly data to expected shape
      const monthlyItems = (monthlyData || []) as unknown as CertificateData[];

      // Process monthly data
      const months = Array.from({ length: 6 }, (_, i) => {
        const date = subDays(now, 30 * (5 - i));
        return {
          month: format(date, 'MMM'),
          issued: 0,
          renewed: 0,
        };
      });

      // Process problematic lifts data
      const liftsMap = new Map<
        string,
        { cost: number; hours: number; incidents: number }
      >();

      // Get PMA certificates for location information
      const { data: pmaCerts, error: pmaError } = await supabase
        .from('pma_certificates')
        .select('id, location');

      if (pmaError) {
        console.error('Error fetching PMA certificates:', pmaError);
        throw pmaError;
      }

      // Create a map of PMA id to location
      const pmaLocations = new Map<string, string>();
      (pmaCerts || []).forEach((cert) => {
        if (cert.id && cert.location) {
          pmaLocations.set(cert.id, cert.location);
        }
      });

      // Calculate metrics for each location
      (maintenanceData || []).forEach((log) => {
        const location =
          log.pma_id && pmaLocations.has(log.pma_id)
            ? pmaLocations.get(log.pma_id)!
            : 'Unknown Location';

        if (!liftsMap.has(location)) {
          liftsMap.set(location, { cost: 0, hours: 0, incidents: 0 });
        }

        const current = liftsMap.get(location)!;

        // Since we don't have actual cost/time data, we'll estimate:
        // - Each maintenance log counts as an incident
        // - Estimated cost based on operation type (simplified)
        // - Estimated hours based on operation type (simplified)
        const estimatedCost =
          log.operation_log_type === 'emergency'
            ? 5000
            : log.operation_log_type === 'corrective'
              ? 2000
              : 500;
        const estimatedHours =
          log.operation_log_type === 'emergency'
            ? 24
            : log.operation_log_type === 'corrective'
              ? 8
              : 2;

        liftsMap.set(location, {
          cost: current.cost + estimatedCost,
          hours: current.hours + estimatedHours,
          incidents: current.incidents + 1,
        });
      });

      // Convert to array and sort by cost (highest first)
      const problematicLifts = Array.from(liftsMap.entries())
        .map(([location, stats]) => ({
          location,
          cost: stats.cost,
          hours: Math.round(stats.hours),
          incidents: stats.incidents,
        }))
        .sort((a, b) => b.cost - a.cost)
        .slice(0, 3); // Get top 3 most expensive lifts

      monthlyItems.forEach((cert) => {
        const createdMonth = cert.created_at
          ? format(new Date(cert.created_at), 'MMM')
          : null;

        const renewalMonth = cert.updated_at
          ? format(new Date(cert.updated_at), 'MMM')
          : null;

        if (createdMonth) {
          const monthIndex = months.findIndex((m) => m.month === createdMonth);
          if (monthIndex >= 0) {
            months[monthIndex].issued += 1;
          }
        }

        if (renewalMonth) {
          const monthIndex = months.findIndex((m) => m.month === renewalMonth);
          if (monthIndex >= 0) {
            months[monthIndex].renewed += 1;
          }
        }
      });

      // Get certificate states instead of categories
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('pma_certificates')
        .select('state, id');

      if (categoriesError) {
        console.error('Error fetching PMA categories:', categoriesError);
        throw categoriesError;
      }

      // Cast categories data to expected shape
      const categoryItems = (categoriesData ||
        []) as unknown as CertificateData[];

      // Process states instead of categories
      const categoriesMap = new Map<string, number>();
      categoryItems.forEach((cert) => {
        if (cert.state) {
          const count = categoriesMap.get(cert.state) || 0;
          categoriesMap.set(cert.state, count + 1);
        }
      });

      const categories = Array.from(categoriesMap.entries()).map(
        ([name, count]) => ({
          name,
          count,
        }),
      );

      // Get recent certificates
      let recentQuery = supabase
        .from('pma_certificates')
        .select('id, pma_number, status, expiry_date, location')
        .order('created_at', { ascending: false })
        .limit(5);

      if (projectId) {
        recentQuery = recentQuery.eq('project_id', projectId);
      }

      const { data: recent, error: recentError } = await recentQuery;

      if (recentError) {
        console.error('Error fetching recent PMAs:', recentError);
        throw recentError;
      }

      // Cast recent data to expected shape
      const recentItems = (recent || []) as unknown as CertificateData[];

      return {
        statusDistribution: [
          { name: 'Valid', value: valid, color: '#4CAF50' },
          { name: 'Expired', value: expired, color: '#F44336' },
          { name: 'Expiring Soon', value: expiringSoon, color: '#FF9800' },
        ],
        monthlyTrend: months,
        categories,
        problematicLifts,
        recent: recentItems.map((cert) => ({
          id: cert.pma_number || cert.id,
          name: cert.location || 'Certificate ' + cert.pma_number,
          status: cert.status || 'Unknown',
          expiryDate: cert.expiry_date || null,
        })),
        total: certificates.length,
        validCount: valid,
        expiredCount: expired,
        expiringSoonCount: expiringSoon,
      };
    } catch (error) {
      console.error('Error in getPMACertificatesStats:', error);
      // Return empty data structure on error
      return {
        statusDistribution: [
          { name: 'Valid', value: 0, color: '#4CAF50' },
          { name: 'Expired', value: 0, color: '#F44336' },
          { name: 'Expiring Soon', value: 0, color: '#FF9800' },
        ],
        monthlyTrend: [],
        categories: [],
        problematicLifts: [],
        recent: [],
        total: 0,
        validCount: 0,
        expiredCount: 0,
        expiringSoonCount: 0,
      };
    }
  },

  /**
   * Get maintenance logs stats
   * @param projectId Optional project ID to filter by
   * @returns Maintenance logs stats
   */
  async getMaintenanceLogsStats(
    projectId?: string,
  ): Promise<MaintenanceLogStats> {
    try {
      // Define the shape of the maintenance logs data we expect
      interface MaintenanceLogData {
        id: string;
        status?: string;
        operation_type?: string;
        operation_log_type?: string;
        description?: string;
        log_date?: string;
        created_at?: string;
        pma_id?: string;
        created_at_time?: string;
      }

      // Base query builder
      let query = supabase
        .from('maintenance_logs')
        .select(
          'id, status, operation_type, operation_log_type, description, log_date, created_at, pma_id',
        );

      // Add project filter if provided
      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      // Execute query
      const { data, error } = await query;

      if (error) {
        console.error('Error fetching maintenance logs:', error);
        throw error;
      }

      // Cast data to expected shape
      const logs = (data || []) as unknown as MaintenanceLogData[];

      // Calculate statistics
      const completed = logs.filter((log) => log.status === 'completed').length;

      const pending = logs.filter(
        (log) => log.status === 'pending' || log.status === 'in_progress',
      ).length;

      const now = new Date();
      const overdue = logs.filter(
        (log) =>
          (log.status === 'pending' || log.status === 'in_progress') &&
          log.log_date &&
          new Date(log.log_date) < now,
      ).length;

      // Get maintenance types
      const typesMap = new Map<string, number>();
      logs.forEach((log) => {
        if (log.operation_type) {
          const count = typesMap.get(log.operation_type) || 0;
          typesMap.set(log.operation_type, count + 1);
        }
      });

      const types = Array.from(typesMap.entries()).map(([name, count]) => ({
        name,
        count,
      }));

      // Get monthly trend data
      const sixMonthsAgo = subDays(now, 180);
      const { data: monthlyData, error: monthlyError } = await supabase
        .from('maintenance_logs')
        .select('id, created_at, operation_type')
        .gte('created_at', format(sixMonthsAgo, 'yyyy-MM-dd'));

      if (monthlyError) {
        console.error('Error fetching monthly maintenance data:', monthlyError);
        throw monthlyError;
      }

      // Cast monthly data to expected shape
      const monthlyItems = (monthlyData ||
        []) as unknown as MaintenanceLogData[];

      // Process monthly data
      const months = Array.from({ length: 6 }, (_, i) => {
        const date = subDays(now, 30 * (5 - i));
        return {
          month: format(date, 'MMM'),
          preventive: 0,
          corrective: 0,
          emergency: 0,
        };
      });

      monthlyItems.forEach((log) => {
        const createdMonth = log.created_at
          ? format(new Date(log.created_at), 'MMM')
          : null;

        if (createdMonth) {
          const monthIndex = months.findIndex((m) => m.month === createdMonth);
          if (monthIndex >= 0) {
            if (log.operation_type === 'preventive') {
              months[monthIndex].preventive += 1;
            } else if (log.operation_type === 'corrective') {
              months[monthIndex].corrective += 1;
            } else if (log.operation_type === 'emergency') {
              months[monthIndex].emergency += 1;
            }
          }
        }
      });

      // Fetch PMA certificates to calculate maintenance metrics
      let pmaQuery = supabase
        .from('pma_certificates')
        .select('id, status')
        .gte('expiry_date', format(now, 'yyyy-MM-dd'));

      if (projectId) {
        pmaQuery = pmaQuery.eq('project_id', projectId);
      }

      const { data: pmaCerts, error: pmaError } = await pmaQuery;

      if (pmaError) {
        console.error('Error fetching PMA certificates:', pmaError);
        throw pmaError;
      }

      // Get current date in YYYY-MM-DD format for today's logs
      const today = format(now, 'yyyy-MM-dd');

      // Fetch today's maintenance logs with timestamp
      const { data: todayLogs, error: todayLogsError } = await supabase
        .from('maintenance_logs')
        .select('id, pma_id, created_at, log_date')
        .eq('log_date', today);

      if (todayLogsError) {
        console.error(
          "Error fetching today's maintenance logs:",
          todayLogsError,
        );
        throw todayLogsError;
      }

      // Calculate the 5 PM cutoff time
      const cutoffHour = 17; // 5 PM in 24-hour format

      // Count logs done after 5 PM
      const overdueMaintenance = todayLogs
        ? todayLogs.filter((log) => {
            if (!log.created_at) return false;
            const logTime = new Date(log.created_at);
            return logTime.getHours() >= cutoffHour;
          }).length
        : 0;

      // Get list of PMA IDs that have maintenance logs for today
      const pmaIdsWithLogs = new Set(
        todayLogs?.map((log) => log.pma_id).filter(Boolean) || [],
      );

      // Count PMAs with logs for today (completed maintenance)
      const completedMaintenance = pmaCerts
        ? pmaCerts.filter((cert) => pmaIdsWithLogs.has(cert.id)).length
        : 0;

      // Total maintenance is the total count of PMA certificates
      const totalMaintenance = pmaCerts ? pmaCerts.length : 0;

      // Get recent logs
      let recentQuery = supabase
        .from('maintenance_logs')
        .select('id, description, operation_type, status, log_date, created_at')
        .order('created_at', { ascending: false })
        .limit(5);

      if (projectId) {
        recentQuery = recentQuery.eq('project_id', projectId);
      }

      const { data: recent, error: recentError } = await recentQuery;

      if (recentError) {
        console.error('Error fetching recent maintenance logs:', recentError);
        throw recentError;
      }

      // Cast recent data to expected shape
      const recentItems = (recent || []) as unknown as MaintenanceLogData[];

      return {
        statusDistribution: [
          { name: 'Completed', value: completed, color: '#4CAF50' },
          { name: 'Pending', value: pending, color: '#FF9800' },
          { name: 'Overdue', value: overdue, color: '#F44336' },
        ],
        byType: types,
        monthlyTrend: months,
        recent: recentItems.map((log) => ({
          id: log.id,
          title: log.description || 'Untitled Maintenance',
          type: log.operation_type || 'Unknown',
          status: log.status || 'Unknown',
          date: log.log_date
            ? format(new Date(log.log_date), 'yyyy-MM-dd')
            : null,
        })),
        total: logs.length,
        completedCount: completed,
        pendingCount: pending,
        overdueCount: overdue,
        completedMaintenance, // PMA certs with maintenance log for today
        totalMaintenance, // Total PMA certificates count
        overdueMaintenance, // Maintenance logs done after 5pm
      };
    } catch (error) {
      console.error('Error in getMaintenanceLogsStats:', error);
      // Return empty data structure on error
      return {
        statusDistribution: [
          { name: 'Completed', value: 0, color: '#4CAF50' },
          { name: 'Pending', value: 0, color: '#FF9800' },
          { name: 'Overdue', value: 0, color: '#F44336' },
        ],
        byType: [],
        monthlyTrend: [],
        recent: [],
        total: 0,
        completedCount: 0,
        pendingCount: 0,
        overdueCount: 0,
        completedMaintenance: 0,
        totalMaintenance: 0,
        overdueMaintenance: 0,
      };
    }
  },

  /**
   * Get complaints stats
   * @param projectId Optional project ID to filter by
   * @returns Complaints stats
   */
  async getComplaintsStats(projectId?: string): Promise<ComplaintStats> {
    try {
      // Define the shape of the complaints data we expect
      interface ComplaintData {
        id: string;
        description?: string;
        cause_of_damage?: string;
        status?: string;
        created_at?: string;
        actual_completion_date?: string;
        location?: string;
      }

      // Base query builder
      let query = supabase
        .from('complaints')
        .select(
          'id, description, cause_of_damage, status, created_at, actual_completion_date, location',
        );

      // Add project filter if provided
      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      // Execute query
      const { data, error } = await query;

      if (error) {
        console.error('Error fetching complaints:', error);
        throw error;
      }

      // Cast data to expected shape
      const complaints = (data || []) as unknown as ComplaintData[];

      // Calculate statistics
      const newCount = complaints.filter(
        (complaint) => complaint.status === 'new',
      ).length;

      const inProgress = complaints.filter(
        (complaint) => complaint.status === 'in_progress',
      ).length;

      const resolved = complaints.filter(
        (complaint) => complaint.status === 'resolved',
      ).length;

      const closed = complaints.filter(
        (complaint) => complaint.status === 'closed',
      ).length;

      // Get categories
      const categoriesMap = new Map<string, number>();
      complaints.forEach((complaint) => {
        if (complaint.cause_of_damage) {
          const count = categoriesMap.get(complaint.cause_of_damage) || 0;
          categoriesMap.set(complaint.cause_of_damage, count + 1);
        }
      });

      const categories = Array.from(categoriesMap.entries()).map(
        ([name, count]) => ({
          name,
          count,
        }),
      );

      // Get monthly trend data
      const now = new Date();
      const twelveMonthsAgo = subDays(now, 365);
      const { data: monthlyData, error: monthlyError } = await supabase
        .from('complaints')
        .select('id, created_at, actual_completion_date, status')
        .gte('created_at', format(twelveMonthsAgo, 'yyyy-MM-dd'));

      if (monthlyError) {
        console.error('Error fetching monthly complaints data:', monthlyError);
        throw monthlyError;
      }

      // Cast monthly data to expected shape
      const monthlyItems = (monthlyData || []) as unknown as ComplaintData[];

      // Process monthly data for regular trend (6 months)
      const months = Array.from({ length: 6 }, (_, i) => {
        const date = subDays(now, 30 * (5 - i));
        return {
          month: format(date, 'MMM'),
          received: 0,
          resolved: 0,
        };
      });

      // Process monthly data for trend analysis (12 months)
      const fullMonthNames = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];

      const trendMonths = Array.from({ length: 12 }, (_, i) => {
        const date = subDays(now, 30 * (11 - i));
        const monthIndex = new Date(date).getMonth();
        return {
          month: fullMonthNames[monthIndex],
          total: 0,
        };
      });

      monthlyItems.forEach((complaint) => {
        const createdMonth = complaint.created_at
          ? format(new Date(complaint.created_at), 'MMM')
          : null;

        const resolvedMonth = complaint.actual_completion_date
          ? format(new Date(complaint.actual_completion_date), 'MMM')
          : null;

        // For regular monthly trend (6 months)
        if (createdMonth) {
          const monthIndex = months.findIndex((m) => m.month === createdMonth);
          if (monthIndex >= 0) {
            months[monthIndex].received += 1;
          }
        }

        if (resolvedMonth) {
          const monthIndex = months.findIndex((m) => m.month === resolvedMonth);
          if (monthIndex >= 0) {
            months[monthIndex].resolved += 1;
          }
        }

        // For trend analysis (12 months)
        if (complaint.created_at) {
          const date = new Date(complaint.created_at);
          const monthIndex = date.getMonth();
          const monthName = fullMonthNames[monthIndex];
          const trendMonthIndex = trendMonths.findIndex(
            (m) => m.month === monthName,
          );

          if (trendMonthIndex >= 0) {
            trendMonths[trendMonthIndex].total += 1;
          }
        }
      });

      // Calculate growth rate for trend analysis
      let trendGrowthRate = 0;
      if (trendMonths.length >= 2) {
        const firstHalfTotal = trendMonths
          .slice(0, 6)
          .reduce((sum, month) => sum + month.total, 0);
        const secondHalfTotal = trendMonths
          .slice(6, 12)
          .reduce((sum, month) => sum + month.total, 0);

        if (firstHalfTotal > 0) {
          trendGrowthRate = Number(
            (
              ((secondHalfTotal - firstHalfTotal) / firstHalfTotal) *
              100
            ).toFixed(1),
          );
        }
      }

      // Calculate response time by category
      const responseTimeMap = new Map<string, number>();
      const responseTimeCountMap = new Map<string, number>();

      complaints.forEach((complaint) => {
        if (
          complaint.cause_of_damage &&
          complaint.created_at &&
          complaint.actual_completion_date &&
          complaint.status === 'resolved'
        ) {
          const createdDate = new Date(complaint.created_at);
          const resolvedDate = new Date(complaint.actual_completion_date);
          const daysToResolve = Math.ceil(
            (resolvedDate.getTime() - createdDate.getTime()) /
              (1000 * 3600 * 24),
          );

          const category = complaint.cause_of_damage;
          const currentTotal = responseTimeMap.get(category) || 0;
          const currentCount = responseTimeCountMap.get(category) || 0;

          responseTimeMap.set(category, currentTotal + daysToResolve);
          responseTimeCountMap.set(category, currentCount + 1);
        }
      });

      const responseTime = Array.from(responseTimeMap.entries()).map(
        ([category, totalDays]) => {
          const count = responseTimeCountMap.get(category) || 1;
          return {
            category,
            avgTime: Number((totalDays / count).toFixed(1)),
          };
        },
      );

      // Get recent complaints
      let recentQuery = supabase
        .from('complaints')
        .select(
          'id, description, cause_of_damage, status, created_at, location',
        )
        .order('created_at', { ascending: false })
        .limit(5);

      if (projectId) {
        recentQuery = recentQuery.eq('project_id', projectId);
      }

      const { data: recent, error: recentError } = await recentQuery;

      if (recentError) {
        console.error('Error fetching recent complaints:', recentError);
        throw recentError;
      }

      // Cast recent data to expected shape
      const recentItems = (recent || []) as unknown as ComplaintData[];

      // Calculate resolution rate
      const resolvedComplaints = complaints.filter(
        (c) => c.status === 'resolved' || c.status === 'closed',
      );
      const resolutionRate =
        complaints.length > 0
          ? Math.round((resolvedComplaints.length / complaints.length) * 100)
          : 0;

      return {
        statusDistribution: [
          { name: 'New', value: newCount, color: '#2196F3' },
          { name: 'In Progress', value: inProgress, color: '#FF9800' },
          { name: 'Resolved', value: resolved, color: '#4CAF50' },
          { name: 'Closed', value: closed, color: '#9E9E9E' },
        ],
        byCategory: categories,
        monthlyTrend: months,
        responseTime,
        trendAnalysis: trendMonths,
        trendGrowthRate,
        recent: recentItems.map((complaint) => ({
          id: complaint.id,
          title: complaint.description || 'Untitled Complaint',
          category:
            complaint.cause_of_damage || complaint.location || 'Uncategorized',
          status: complaint.status || 'Unknown',
          date: complaint.created_at
            ? format(new Date(complaint.created_at), 'yyyy-MM-dd')
            : null,
        })),
        total: complaints.length,
        newCount,
        inProgressCount: inProgress,
        resolvedCount: resolved,
        closedCount: closed,
        openCount: newCount + inProgress,
        resolutionRate,
      };
    } catch (error) {
      console.error('Error in getComplaintsStats:', error);
      // Return empty data structure on error
      return {
        statusDistribution: [
          { name: 'New', value: 0, color: '#2196F3' },
          { name: 'In Progress', value: 0, color: '#FF9800' },
          { name: 'Resolved', value: 0, color: '#4CAF50' },
          { name: 'Closed', value: 0, color: '#9E9E9E' },
        ],
        byCategory: [],
        monthlyTrend: [],
        responseTime: [],
        trendAnalysis: [],
        trendGrowthRate: 0,
        recent: [],
        total: 0,
        newCount: 0,
        inProgressCount: 0,
        resolvedCount: 0,
        closedCount: 0,
        openCount: 0,
        resolutionRate: 0,
      };
    }
  },

  /**
   * Get safety incident stats
   * @param _projectId Optional project ID to filter by
   * @returns Safety incident stats
   */
  async getSafetyIncidentStats(
    _projectId?: string,
  ): Promise<SafetyIncidentStats> {
    // This is a placeholder - you would need to implement this based on your actual safety incidents table
    // For now, returning mock data
    return {
      last30Days: 0,
      trend: [
        { month: 'Jan', count: 0 },
        { month: 'Feb', count: 0 },
        { month: 'Mar', count: 0 },
        { month: 'Apr', count: 0 },
        { month: 'May', count: 0 },
        { month: 'Jun', count: 0 },
      ],
    };
  },

  /**
   * Get compliance score
   * @param projectId Optional project ID to filter by
   * @returns Compliance score
   */
  async getComplianceScore(projectId?: string): Promise<ComplianceScore> {
    try {
      // Get PMA certificates
      const pmaStats = await this.getPMACertificatesStats(projectId);

      // Get maintenance logs
      const maintenanceStats = await this.getMaintenanceLogsStats(projectId);

      // Calculate compliance score based on:
      // 1. Percentage of valid PMA certificates
      // 2. Percentage of on-time maintenance logs

      const pmaCompliance =
        pmaStats.total > 0 ? (pmaStats.validCount / pmaStats.total) * 100 : 100;

      const maintenanceCompliance =
        maintenanceStats.total > 0
          ? ((maintenanceStats.total - maintenanceStats.overdueCount) /
              maintenanceStats.total) *
            100
          : 100;

      // Overall compliance score (weighted average)
      const complianceScore = Math.round(
        pmaCompliance * 0.6 + maintenanceCompliance * 0.4,
      );

      return {
        score: complianceScore,
        pmaCompliance: Math.round(pmaCompliance),
        maintenanceCompliance: Math.round(maintenanceCompliance),
      };
    } catch (error) {
      console.error('Error in getComplianceScore:', error);
      return {
        score: 0,
        pmaCompliance: 0,
        maintenanceCompliance: 0,
      };
    }
  },
};
