import { z } from 'zod';

export const OPERATION_LOG_TYPES = [
  'daily logs',
  'second schedule',
  'mantrap',
] as const;

export const MAINTENANCE_STATUS = [
  'fully function',
  'partially function',
  'broken',
] as const;

export const createMaintenanceLogSchema = z.object({
  log_date: z.date(),
  operation_log_type: z.enum(OPERATION_LOG_TYPES),
  contractor_id: z.string().min(1, { message: 'Contractor is required' }),
  pma_id: z.string().optional(),
  description: z.string().min(10, {
    message: 'Description must be at least 10 characters long',
  }),
  status: z.enum(MAINTENANCE_STATUS),
});

export type CreateMaintenanceLogInput = z.infer<
  typeof createMaintenanceLogSchema
>;

export const OPERATION_TYPE_COLORS: Record<
  (typeof OPERATION_LOG_TYPES)[number],
  string
> = {
  'daily logs': 'bg-blue-50 text-blue-700 border-blue-100',
  'second schedule': 'bg-amber-50 text-amber-700 border-amber-100',
  mantrap: 'bg-red-50 text-red-700 border-red-100',
};

export const MAINTENANCE_STATUS_COLORS: Record<
  (typeof MAINTENANCE_STATUS)[number],
  string
> = {
  'fully function': 'bg-green-50 text-green-700 border-green-100',
  'partially function': 'bg-amber-50 text-amber-700 border-amber-100',
  broken: 'bg-red-50 text-red-700 border-red-100',
};
