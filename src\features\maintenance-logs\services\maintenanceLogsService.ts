import { supabase } from '@/lib/supabase';
import type { Database } from '@/types/database';
import type {
  MaintenanceLogsFilters,
  MaintenanceLogsType,
} from '../types/table';
import {
  applyPagination,
  applySorting,
  buildQuery,
  transformData,
} from '../utils/query';

type MaintenanceLogInsert =
  Database['public']['Tables']['maintenance_logs']['Insert'];
type MaintenanceLogUpdate =
  Database['public']['Tables']['maintenance_logs']['Update'];

export interface MaintenanceLogsParams {
  projectId: string;
  filters: MaintenanceLogsFilters;
  pageIndex: number;
  pageSize: number;
  sorting?: {
    column?: string;
    direction?: 'asc' | 'desc';
  };
}

export interface MaintenanceLogsResponse {
  data: MaintenanceLogsType[];
  totalCount: number;
}

/**
 * Fetch maintenance logs with filters, pagination, and sorting
 */
export async function fetchMaintenanceLogs(
  params: MaintenanceLogsParams,
): Promise<MaintenanceLogsResponse> {
  const { projectId, filters, pageIndex, pageSize, sorting } = params;

  try {
    let query = buildQuery(supabase.from('maintenance_logs'), filters).eq(
      'project_id',
      projectId,
    );

    // Apply sorting
    query = applySorting(query, sorting?.column, sorting?.direction);

    // Apply pagination
    query = applyPagination(query, pageIndex, pageSize);

    console.log('Executing query...');
    const response = await query;
    console.log('Query response:', {
      data: response.data,
      error: response.error,
      count: response.count,
    });

    if (response.error) {
      console.error('Supabase query error:', response.error);
      throw new Error(
        `Failed to fetch maintenance logs: ${response.error.message}`,
      );
    }

    const data = (response.data || []) as Record<string, unknown>[];
    console.log('Raw data:', data);

    const transformedData = transformData(data);
    console.log('Transformed data:', transformedData);

    return {
      data: transformedData,
      totalCount: response.count || 0,
    };
  } catch (error) {
    console.error('Error fetching maintenance logs:', error);
    throw error instanceof Error
      ? error
      : new Error('Unknown error occurred while fetching maintenance logs');
  }
}

/**
 * Create a new maintenance log
 */
export async function createMaintenanceLog(data: MaintenanceLogInsert) {
  try {
    const response = await supabase
      .from('maintenance_logs')
      .insert(data)
      .select();

    if (response.error) {
      throw new Error(
        `Failed to create maintenance log: ${response.error.message}`,
      );
    }

    // Check if we got data back
    if (!response.data || response.data.length === 0) {
      throw new Error('Failed to create maintenance log: No data returned');
    }

    // Return the first (and should be only) created record
    return response.data[0];
  } catch (error) {
    console.error('Error creating maintenance log:', error);
    throw error instanceof Error
      ? error
      : new Error('Unknown error occurred while creating maintenance log');
  }
}

/**
 * Update a maintenance log
 */
export async function updateMaintenanceLog(
  id: string,
  data: MaintenanceLogUpdate,
) {
  try {
    // First check if the record exists
    const existsResponse = await supabase
      .from('maintenance_logs')
      .select('id')
      .eq('id', id)
      .maybeSingle();

    if (existsResponse.error) {
      throw new Error(
        `Failed to check maintenance log existence: ${existsResponse.error.message}`,
      );
    }

    if (!existsResponse.data) {
      throw new Error(`Maintenance log with ID ${id} not found`);
    }

    // Now perform the update
    const response = await supabase
      .from('maintenance_logs')
      .update(data)
      .eq('id', id)
      .select();

    if (response.error) {
      throw new Error(
        `Failed to update maintenance log: ${response.error.message}`,
      );
    }

    // Check if we got data back
    if (!response.data || response.data.length === 0) {
      throw new Error('Failed to update maintenance log: No data returned');
    }

    // Return the first (and should be only) updated record
    return response.data[0];
  } catch (error) {
    console.error('Error updating maintenance log:', error);
    throw error instanceof Error
      ? error
      : new Error('Unknown error occurred while updating maintenance log');
  }
}

/**
 * Delete a maintenance log
 */
export async function deleteMaintenanceLog(id: string) {
  try {
    const response = await supabase
      .from('maintenance_logs')
      .delete()
      .eq('id', id);

    if (response.error) {
      throw new Error(
        `Failed to delete maintenance log: ${response.error.message}`,
      );
    }

    return response.data;
  } catch (error) {
    console.error('Error deleting maintenance log:', error);
    throw error instanceof Error
      ? error
      : new Error('Unknown error occurred while deleting maintenance log');
  }
}

export interface MaintenanceStatsResponse {
  total: number;
  dailyLogs: number;
  secondSchedule: number;
  mantrap: number;
  thisMonth: number;
  lastMonth: number;
  last7Days: number;
  monthChange: number;
  weekTrend: Array<{ date: string; count: number }>;
}

/**
 * Fetch maintenance statistics for status cards
 */
export async function fetchMaintenanceStats(
  projectId: string,
  filters: MaintenanceLogsFilters,
): Promise<MaintenanceStatsResponse> {
  try {
    console.log('Fetching maintenance stats for project:', projectId);

    const now = new Date();
    const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
    const last7DaysStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Execute queries in parallel with proper query construction
    const [
      totalResult,
      dailyLogsResult,
      secondScheduleResult,
      mantrapResult,
      thisMonthResult,
      lastMonthResult,
      last7DaysResult,
      weekTrendResult,
    ] = await Promise.all([
      // Total count with filters - build fresh query
      (() => {
        let query = supabase
          .from('maintenance_logs')
          .select('*', { count: 'exact', head: true })
          .eq('project_id', projectId);

        if (filters.dateRange?.from) {
          query = query.gte(
            'log_date',
            filters.dateRange.from.toISOString().split('T')[0],
          );
        }
        if (filters.dateRange?.to) {
          query = query.lte(
            'log_date',
            filters.dateRange.to.toISOString().split('T')[0],
          );
        }
        if (filters.search?.trim()) {
          const searchTerm = filters.search.trim();
          query = query.or(
            `description.ilike.%${searchTerm}%,person_in_charge_name.ilike.%${searchTerm}%`,
          );
        }
        return query;
      })(),
      // Daily logs count
      (() => {
        let query = supabase
          .from('maintenance_logs')
          .select('*', { count: 'exact', head: true })
          .eq('project_id', projectId)
          .eq('operation_log_type', 'daily logs');

        if (filters.dateRange?.from) {
          query = query.gte(
            'log_date',
            filters.dateRange.from.toISOString().split('T')[0],
          );
        }
        if (filters.dateRange?.to) {
          query = query.lte(
            'log_date',
            filters.dateRange.to.toISOString().split('T')[0],
          );
        }
        if (filters.search?.trim()) {
          const searchTerm = filters.search.trim();
          query = query.or(
            `description.ilike.%${searchTerm}%,person_in_charge_name.ilike.%${searchTerm}%`,
          );
        }
        return query;
      })(),
      // Second schedule count
      (() => {
        let query = supabase
          .from('maintenance_logs')
          .select('*', { count: 'exact', head: true })
          .eq('project_id', projectId)
          .eq('operation_log_type', 'second schedule');

        if (filters.dateRange?.from) {
          query = query.gte(
            'log_date',
            filters.dateRange.from.toISOString().split('T')[0],
          );
        }
        if (filters.dateRange?.to) {
          query = query.lte(
            'log_date',
            filters.dateRange.to.toISOString().split('T')[0],
          );
        }
        if (filters.search?.trim()) {
          const searchTerm = filters.search.trim();
          query = query.or(
            `description.ilike.%${searchTerm}%,person_in_charge_name.ilike.%${searchTerm}%`,
          );
        }
        return query;
      })(),
      // Mantrap count
      (() => {
        let query = supabase
          .from('maintenance_logs')
          .select('*', { count: 'exact', head: true })
          .eq('project_id', projectId)
          .eq('operation_log_type', 'mantrap');

        if (filters.dateRange?.from) {
          query = query.gte(
            'log_date',
            filters.dateRange.from.toISOString().split('T')[0],
          );
        }
        if (filters.dateRange?.to) {
          query = query.lte(
            'log_date',
            filters.dateRange.to.toISOString().split('T')[0],
          );
        }
        if (filters.search?.trim()) {
          const searchTerm = filters.search.trim();
          query = query.or(
            `description.ilike.%${searchTerm}%,person_in_charge_name.ilike.%${searchTerm}%`,
          );
        }
        return query;
      })(),
      // This month (no filters, just project and date)
      supabase
        .from('maintenance_logs')
        .select('*', { count: 'exact', head: true })
        .eq('project_id', projectId)
        .gte('log_date', thisMonthStart.toISOString().split('T')[0]),
      // Last month
      supabase
        .from('maintenance_logs')
        .select('*', { count: 'exact', head: true })
        .eq('project_id', projectId)
        .gte('log_date', lastMonthStart.toISOString().split('T')[0])
        .lte('log_date', lastMonthEnd.toISOString().split('T')[0]),
      // Last 7 days
      supabase
        .from('maintenance_logs')
        .select('*', { count: 'exact', head: true })
        .eq('project_id', projectId)
        .gte('log_date', last7DaysStart.toISOString().split('T')[0]),
      // Week trend data
      supabase
        .from('maintenance_logs')
        .select('log_date')
        .eq('project_id', projectId)
        .gte('log_date', last7DaysStart.toISOString().split('T')[0])
        .order('log_date', { ascending: true }),
    ]);

    // Check for errors
    [
      totalResult,
      dailyLogsResult,
      secondScheduleResult,
      mantrapResult,
      thisMonthResult,
      lastMonthResult,
      last7DaysResult,
      weekTrendResult,
    ].forEach((result, index) => {
      if (result.error) {
        console.error(`Query ${index} error:`, result.error);
        throw new Error(
          `Failed to fetch maintenance stats: ${result.error.message}`,
        );
      }
    });

    const total = totalResult.count || 0;
    const thisMonth = thisMonthResult.count || 0;
    const lastMonth = lastMonthResult.count || 0;
    const monthChange =
      lastMonth > 0 ? ((thisMonth - lastMonth) / lastMonth) * 100 : 0;

    // Process week trend data
    const weekTrend = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      const count =
        weekTrendResult.data?.filter((log) => log.log_date === dateStr)
          .length || 0;

      weekTrend.push({
        date: dateStr,
        count,
      });
    }

    const stats: MaintenanceStatsResponse = {
      total,
      dailyLogs: dailyLogsResult.count || 0,
      secondSchedule: secondScheduleResult.count || 0,
      mantrap: mantrapResult.count || 0,
      thisMonth,
      lastMonth,
      last7Days: last7DaysResult.count || 0,
      monthChange: Math.round(monthChange),
      weekTrend,
    };

    console.log('Maintenance stats:', stats);
    return stats;
  } catch (error) {
    console.error('Error fetching maintenance stats:', error);
    throw error instanceof Error
      ? error
      : new Error('Unknown error occurred while fetching maintenance stats');
  }
}
