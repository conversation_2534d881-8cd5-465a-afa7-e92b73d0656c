import type { SupabaseClient } from '@supabase/supabase-js';
import { OPERATION_LOG_TYPES } from '../schemas/create-maintenance-log';
import type {
  MaintenanceLogsType,
  MaintenanceTableState,
} from '../types/table';

/**
 * Safely converts any value to a string
 */
export function safeString(value: unknown): string {
  if (value === null || value === undefined) return '';
  return String(value).trim();
}

/**
 * Type guard for operation log types
 */
export function isValidOperationType(
  type: unknown,
): type is (typeof OPERATION_LOG_TYPES)[number] {
  if (typeof type !== 'string') return false;
  return OPERATION_LOG_TYPES.includes(
    type as (typeof OPERATION_LOG_TYPES)[number],
  );
}

/**
 * Type guard for maintenance status
 */
export function isValidMaintenanceStatus(
  status: unknown,
): status is 'fully function' | 'partially function' | 'broken' {
  if (typeof status !== 'string') return false;
  return ['fully function', 'partially function', 'broken'].includes(status);
}

/**
 * Builds a base query with filters applied
 */
export function buildQuery(
  query: ReturnType<SupabaseClient['from']>,
  filters: MaintenanceTableState['filters'],
  options: { withCount?: boolean } = { withCount: true },
) {
  try {
    console.log('Building query with filters:', filters);

    // Base query with all fields including contractor name and created_by user via left joins
    let filteredQuery = query.select(
      `
            id,
            log_date,
            operation_log_type,
            contractor_id,
            person_in_charge_name,
            person_in_charge_phone,
            description,
            created_at,
            created_by,
            project_id,
            pma_id,
            status,
            contractors (
                name
            ),
            pma_certificates (
                pma_number
            ),
            users!created_by (
                name
            )
        `,
      { count: options.withCount ? 'exact' : undefined },
    );

    // Apply search filter - search across multiple fields
    if (filters.search?.trim()) {
      const searchTerm = filters.search.trim();
      filteredQuery = filteredQuery.or(
        `description.ilike.%${searchTerm}%,person_in_charge_name.ilike.%${searchTerm}%,contractors.name.ilike.%${searchTerm}%`,
      );
    }

    // Apply operation type filter
    if (filters.operationType) {
      filteredQuery = filteredQuery.eq(
        'operation_log_type',
        filters.operationType,
      );
    }

    // Apply status filter
    if (filters.status) {
      filteredQuery = filteredQuery.eq('status', filters.status);
    }

    // Apply date range filter
    if (filters.dateRange?.from) {
      filteredQuery = filteredQuery.gte(
        'log_date',
        filters.dateRange.from.toISOString().split('T')[0],
      );
    }
    if (filters.dateRange?.to) {
      filteredQuery = filteredQuery.lte(
        'log_date',
        filters.dateRange.to.toISOString().split('T')[0],
      );
    }

    return filteredQuery;
  } catch (error) {
    console.error('Error building query:', error);
    throw error;
  }
}

/**
 * Transform database records to application data model
 */
export function transformData(
  data: Record<string, unknown>[],
): MaintenanceLogsType[] {
  return data.map((row) => {
    // Safely extract contractor name from join
    let contractorName = 'Unknown Contractor';
    const contractorsField = row.contractors;
    if (contractorsField && typeof contractorsField === 'object') {
      if (Array.isArray(contractorsField)) {
        const first = contractorsField[0] as Record<string, unknown>;
        if (first && typeof first.name === 'string') {
          contractorName = first.name;
        }
      } else {
        const obj = contractorsField as Record<string, unknown>;
        if (typeof obj.name === 'string') {
          contractorName = obj.name;
        }
      }
    }

    // Safely extract PMA number
    let pmaNumber: string | null = null;
    const pmaField = row.pma_certificates;
    if (pmaField && typeof pmaField === 'object') {
      if (Array.isArray(pmaField)) {
        const firstPma = pmaField[0] as Record<string, unknown>;
        if (firstPma && typeof firstPma.pma_number === 'string') {
          pmaNumber = firstPma.pma_number;
        }
      } else {
        const obj = pmaField as Record<string, unknown>;
        if (typeof obj.pma_number === 'string') {
          pmaNumber = obj.pma_number;
        }
      }
    }

    // Safely extract created_by user name
    let createdByName = 'System';
    const usersField = row.users;
    if (usersField && typeof usersField === 'object') {
      if (Array.isArray(usersField)) {
        const firstUser = usersField[0] as Record<string, unknown>;
        if (firstUser && typeof firstUser.name === 'string') {
          createdByName = firstUser.name;
        }
      } else {
        const obj = usersField as Record<string, unknown>;
        if (typeof obj.name === 'string') {
          createdByName = obj.name;
        }
      }
    }

    return {
      id: String(row.id),
      log_date: String(row.log_date),
      operation_log_type: isValidOperationType(row.operation_log_type)
        ? row.operation_log_type
        : 'daily logs', // Default to first new type
      contractor_id: safeString(row.contractor_id),
      contractor_name: safeString(contractorName),
      person_in_charge_name: safeString(row.person_in_charge_name),
      person_in_charge_phone: safeString(row.person_in_charge_phone),
      description: safeString(row.description),
      created_at: safeString(row.created_at || new Date().toISOString()),
      created_by: safeString(createdByName),
      project_id: safeString(row.project_id),
      pma_id: safeString(row.pma_id),
      pma_number: pmaNumber ? safeString(pmaNumber) : null,
      status: isValidMaintenanceStatus(row.status)
        ? row.status
        : 'fully function',
    };
  });
}

/**
 * Apply pagination to a query
 */
export function applyPagination<
  T extends { range: (from: number, to: number) => T },
>(query: T, pageIndex: number, pageSize: number) {
  return query.range(pageIndex * pageSize, (pageIndex + 1) * pageSize - 1);
}

/**
 * Apply sorting to a query
 */
export function applySorting<
  T extends { order: (column: string, options?: { ascending: boolean }) => T },
>(query: T, column?: string, direction: 'asc' | 'desc' = 'desc') {
  if (!column) {
    return query.order('log_date', { ascending: false });
  }

  return query.order(column, {
    ascending: direction === 'asc',
  });
}
