'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { usePmaManagementTranslations } from '@/hooks/use-translations';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus, Trash2 } from 'lucide-react';
import { useFieldArray, useForm } from 'react-hook-form';
import * as z from 'zod';
import { pmaFormSchema } from '../schemas/pma-schema';

const pmasListSchema = z.object({
  pmas: z.array(pmaFormSchema).min(1, 'At least one PMA is required.'),
});

type PmasListSchema = z.infer<typeof pmasListSchema>;

interface PmaFormProps {
  onSubmit: (data: PmasListSchema) => void;
  initialData?: Partial<PmasListSchema>;
  isLoading?: boolean;
  isEditMode?: boolean;
}

export function PmaForm({
  onSubmit,
  initialData,
  isLoading,
  isEditMode = false,
}: PmaFormProps) {
  const t = usePmaManagementTranslations();

  const form = useForm<PmasListSchema>({
    resolver: zodResolver(pmasListSchema),
    defaultValues: initialData || {
      pmas: [{ location: '', pmaNumber: '', dateReceived: '' }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'pmas',
  });

  const handleSubmit = (data: PmasListSchema) => {
    onSubmit(data);
  };

  const addPma = () => {
    append({
      location: '',
      pmaNumber: '',
      dateReceived: '',
    });
  };

  const removePma = (index: number) => {
    remove(index);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-7">
        <div className="space-y-6">
          {fields.map((field, index) => (
            <div
              key={field.id}
              className="relative bg-white/60 backdrop-blur-sm rounded-xl border border-slate-200/60 p-6 shadow-xl shadow-slate-200/20 space-y-6"
            >
              <div className="flex items-center justify-between pb-4 border-b border-slate-200/80">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-slate-800">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  {t('form.pmaEntry', { number: index + 1 })}
                </h3>
                {fields.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removePma(index)}
                    className="text-destructive hover:text-destructive hover:bg-destructive/10"
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    {t('form.remove')}
                  </Button>
                )}
              </div>
              {/* PMA Number */}
              <FormField
                control={form.control}
                name={`pmas.${index}.pmaNumber`}
                render={({ field: formField }) => (
                  <FormItem>
                    <FormLabel>{t('form.pmaNumber')}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('form.pmaNumberPlaceholder')}
                        {...formField}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Date Received */}
              <FormField
                control={form.control}
                name={`pmas.${index}.dateReceived`}
                render={({ field: formField }) => (
                  <FormItem>
                    <FormLabel>{t('form.dateReceived')}</FormLabel>
                    <FormControl>
                      <Input type="date" {...formField} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Location */}
              <FormField
                control={form.control}
                name={`pmas.${index}.location`}
                render={({ field: formField }) => (
                  <FormItem>
                    <FormLabel>{t('form.location')}</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t('form.locationPlaceholder')}
                        className="min-h-[120px] resize-none"
                        {...formField}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          ))}
          {form.formState.errors.pmas &&
            typeof form.formState.errors.pmas === 'object' &&
            'message' in form.formState.errors.pmas && (
              <p className="text-sm font-medium text-destructive">
                {form.formState.errors.pmas.message}
              </p>
            )}
        </div>

        {!isEditMode && (
          <Button
            type="button"
            variant="outline"
            onClick={addPma}
            className="w-full md:w-auto"
          >
            <Plus className="h-4 w-4 mr-2" />
            {t('actions.addAnotherPma')}
          </Button>
        )}

        {/* Submit Button */}
        <div className="flex justify-end pt-4">
          <Button
            type="submit"
            size="lg"
            disabled={isLoading}
            className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white" />
            ) : isEditMode ? (
              t('actions.saveChanges')
            ) : (
              t('actions.submitPma')
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
