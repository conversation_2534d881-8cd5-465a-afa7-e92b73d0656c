'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useProjectContext } from '@/providers/project-context';
import {
  ArrowUpRight,
  Building2,
  Calendar,
  CheckCircle2,
  Clock,
  MapPin,
  MoreVertical,
  Shield,
  User,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { ProjectCardProps } from '../types/project';
import {
  calculateProjectDuration,
  formatDate,
  getStatusColor,
} from '../utils/project-utils';

/**
 * Individual project card component
 */
export const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  onEdit: _onEdit,
  onDelete: _onDelete,
}) => {
  const { selectProject, setSelectedProject } = useProjectContext();
  const router = useRouter();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Clock className="h-4 w-4" />;
      case 'pending':
        return <Calendar className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle2 className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const handleCardClick = () => {
    selectProject(project.id);
    setSelectedProject(project);
    router.push('/dashboard');
  };

  return (
    <Card
      className="border-0 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
      onClick={handleCardClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Building2 className="h-5 w-5 text-primary" />
            </div>
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg font-semibold text-gray-900 line-clamp-2">
                {project.name}
              </CardTitle>
              <CardDescription className="flex items-center mt-1">
                <span className="font-medium text-primary">{project.code}</span>
              </CardDescription>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={(e) => e.stopPropagation()}
          >
            <MoreVertical className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Location */}
          <div className="flex items-center text-sm text-gray-600">
            <MapPin className="h-4 w-4 mr-2 text-gray-400" />
            <span className="line-clamp-1">{project.location}</span>
          </div>

          {/* Dates */}
          <div className="flex items-center text-sm text-gray-600">
            <Calendar className="h-4 w-4 mr-2 text-gray-400" />
            <span>
              {formatDate(project.start_date)}
              {project.end_date && project.start_date && (
                <>
                  {' → '}
                  {formatDate(project.end_date)}
                  <span className="ml-2 text-gray-500">
                    (
                    {calculateProjectDuration(
                      project.start_date,
                      project.end_date,
                    )}
                    )
                  </span>
                </>
              )}
            </span>
          </div>

          {/* Agency */}
          {project.agency && (
            <div className="flex items-center text-sm text-gray-600">
              <Shield className="h-4 w-4 mr-2 text-gray-400" />
              <span className="line-clamp-1">
                {project.agency.name}
                {project.agency.state && ` (${project.agency.state})`}
              </span>
            </div>
          )}

          {/* JKR PICs - Now showing admin users from project_users */}
          {project.project_users &&
            project.project_users.filter((pu) => pu.user.user_role === 'admin')
              .length > 0 && (
              <div className="flex items-start text-sm text-gray-600">
                <User className="h-4 w-4 mr-2 mt-0.5 text-gray-400" />
                <div className="flex-1">
                  <span className="text-gray-500 text-xs uppercase tracking-wide">
                    JKR PIC:
                  </span>
                  <div className="mt-1 space-y-1">
                    {project.project_users
                      .filter((pu) => pu.user.user_role === 'admin')
                      .slice(0, 2)
                      .map((pic, index) => (
                        <div key={index} className="text-xs">
                          {pic.user.name}
                        </div>
                      ))}
                    {project.project_users.filter(
                      (pu) => pu.user.user_role === 'admin',
                    ).length > 2 && (
                      <div className="text-xs text-gray-500">
                        +
                        {project.project_users.filter(
                          (pu) => pu.user.user_role === 'admin',
                        ).length - 2}{' '}
                        more
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

          {/* Status */}
          <div className="flex items-center justify-between">
            <Badge
              variant="secondary"
              className={`${getStatusColor(project.status || '')} text-xs font-medium`}
            >
              <span className="flex items-center gap-1">
                {getStatusIcon(project.status || '')}
                {(project.status ?? '')
                  ? `${String(project.status).charAt(0).toUpperCase()}${String(project.status).slice(1)}`
                  : ''}
              </span>
            </Badge>

            <Button
              variant="ghost"
              size="sm"
              className="h-8 text-primary hover:text-primary"
              onClick={(e) => {
                e.stopPropagation();
                handleCardClick();
              }}
            >
              <ArrowUpRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
