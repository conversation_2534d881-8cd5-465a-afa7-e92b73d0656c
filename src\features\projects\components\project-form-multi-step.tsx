'use client';

import { UnsavedChangesDialog } from '@/components/ui/unsaved-changes-dialog';
import { useCreatePMACertificate } from '@/features/pma-management/hooks/use-create-pma-certificate';
import {
  type PMACertificateStatus,
  type StateCode,
} from '@/features/pma-management/types/create-pma';
import { useUnsavedChanges } from '@/hooks/use-unsaved-changes';
import { uploadToOBS } from '@/lib/obs-upload';
import { Building2 } from 'lucide-react';
import { useState } from 'react';
import {
  type PmasSchema,
  type ProjectDetailsSchema,
} from '../schemas/project-schema';
import { type ProjectFormData, type ProjectFormProps } from '../types/project';
import { PmasStep, usePmasForm } from './pmas-step';
import {
  ProjectDetailsStep,
  useProjectDetailsForm,
} from './project-details-step';

enum FormStep {
  PROJECT_DETAILS = 1,
  PMA = 2,
}

/**
 * Multi-step project creation form
 * Orchestrates the two form steps: Project Details and PMA
 *
 * @important The parent component must implement onSubmit to return the created project data:
 * @example
 * ```ts
 * onSubmit: async (data) => {
 *   const project = await createProject(data);
 *   return { id: project.id, code: project.code };
 * }
 * ```
 */
export function ProjectFormMultiStep({
  initialData,
  onSubmit,
  onCancel,
  isLoading,
}: ProjectFormProps) {
  const [currentStep, setCurrentStep] = useState(FormStep.PROJECT_DETAILS);
  const [formData, setFormData] = useState<Partial<ProjectFormData>>(
    initialData || {},
  );

  // Initialize form instances for each step
  const projectDetailsForm = useProjectDetailsForm({
    ...formData,
    status: formData.status as
      | 'pending'
      | 'active'
      | 'completed'
      | 'cancelled'
      | undefined,
    state: formData.state as
      | 'JH'
      | 'KD'
      | 'KT'
      | 'ML'
      | 'NS'
      | 'PH'
      | 'PN'
      | 'PK'
      | 'PL'
      | 'SB'
      | 'SW'
      | 'SL'
      | 'TR'
      | 'WP'
      | 'LBN'
      | 'PW'
      | 'OTH'
      | undefined,
  });
  const pmasForm = usePmasForm(formData.pmas);

  // Check if any form has unsaved changes
  const hasUnsavedChanges =
    projectDetailsForm.formState.isDirty || pmasForm.formState.isDirty;

  // Unsaved changes warning
  const { showConfirmDialog, confirmNavigation, cancelNavigation, markSaved } =
    useUnsavedChanges({
      hasUnsavedChanges,
      message:
        'You have unsaved project details. Are you sure you want to leave this page?',
      onNavigateAway: () => {
        // User navigated away with unsaved changes
      },
    });

  const handleNext = (stepData: ProjectDetailsSchema) => {
    switch (currentStep) {
      case FormStep.PROJECT_DETAILS:
        setFormData((prev) => ({
          ...prev,
          ...(stepData as ProjectDetailsSchema),
        }));
        setCurrentStep(FormStep.PMA);
        break;
    }
  };

  const handlePrevious = () => {
    if (currentStep > FormStep.PROJECT_DETAILS) {
      setCurrentStep(currentStep - 1);
    }
  };

  const createPMACertificate = useCreatePMACertificate();

  const handleFinalSubmit = async (pmasData: PmasSchema) => {
    try {
      const completeData: ProjectFormData = {
        ...formData,
        pmas: pmasData,
      } as ProjectFormData;

      // First submit the project to get the project data
      let createdProject: { id: string; code: string };
      try {
        createdProject = await onSubmit(completeData);
      } catch (error) {
        // Let the parent component handle the error toast
        throw error;
      }

      // Upload files first
      try {
        const uploadPromises = pmasData.pmas.map(async (pma) => {
          if (!pma.file) {
            throw new Error(`File is required for PMA ${pma.pma_number}`);
          }

          try {
            const fileUrl = await uploadToOBS({
              file: pma.file,
              folder: `pma-certificates/${createdProject.id}`,
            });
            return { pma, fileUrl };
          } catch (uploadError) {
            throw new Error(
              `Failed to upload file for PMA ${pma.pma_number}. Please try again. Original error: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`,
            );
          }
        });

        const uploadResults = await Promise.all(uploadPromises);

        // After files are uploaded, create PMA certificates
        for (const { pma, fileUrl } of uploadResults) {
          try {
            const pmaData = {
              pma_number: pma.pma_number,
              expiry_date: pma.expiry_date,
              status: 'validating' as PMACertificateStatus,
              project_id: createdProject.id,
              location: pma.location,
              file_url: fileUrl,
              state: (completeData.state as StateCode) || null,
              competent_person_id: pma.competent_person_id, // Include competent person ID
            };

            // Wait for the mutation to complete
            const result = await createPMACertificate.mutateAsync(pmaData);

            if (!result.pma_number) {
              throw new Error(`PMA number not saved for ${pma.pma_number}`);
            }
          } catch (certError) {
            throw new Error(
              `Failed to create PMA certificate for ${pma.pma_number}. Please try again. Original error: ${certError instanceof Error ? certError.message : 'Unknown error'}`,
            );
          }
        }

        // Mark changes as saved before submitting
        markSaved();
      } catch (error) {
        // Re-throw to be caught by outer catch
        throw error;
      }
    } catch (error) {
      // Re-throw error to be handled by parent component
      throw error;
    }
  };

  // Enhanced cancel handler that checks for unsaved changes
  const handleCancel = () => {
    if (hasUnsavedChanges) {
      if (
        window.confirm(
          'You have unsaved changes. Are you sure you want to cancel?',
        )
      ) {
        markSaved();
        onCancel?.();
      }
    } else {
      onCancel?.();
    }
  };

  return (
    <div className="bg-card rounded-3xl shadow-lg p-8 lg:p-12 border max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-foreground mb-2 flex items-center gap-3">
          <Building2 className="h-6 w-6 text-primary" />
          Create New Project
        </h1>
        <p className="text-muted-foreground">
          Set up your project with all necessary details and documentation
        </p>
      </div>

      {/* Progress Indicator removed as requested */}

      {/* Current Step Content */}
      <div className="space-y-8">
        {currentStep === FormStep.PROJECT_DETAILS && (
          <ProjectDetailsStep
            form={projectDetailsForm}
            onNext={handleNext}
            onCancel={handleCancel}
            isLoading={isLoading}
          />
        )}

        {currentStep === FormStep.PMA && (
          <PmasStep
            form={pmasForm}
            onSubmit={handleFinalSubmit}
            onPrevious={handlePrevious}
            onCancel={handleCancel}
            isLoading={isLoading}
          />
        )}
      </div>

      {/* Footer */}
      <div className="mt-8 pt-6 border-t border-border">
        <p className="text-sm text-muted-foreground text-center">
          All fields marked with <span className="text-destructive">*</span> are
          required
        </p>
      </div>

      {/* Unsaved Changes Dialog */}
      <UnsavedChangesDialog
        open={showConfirmDialog}
        onConfirm={confirmNavigation}
        onCancel={cancelNavigation}
        description="You have unsaved changes that will be lost if you leave this page. Are you sure you want to continue?"
      />
    </div>
  );
}
