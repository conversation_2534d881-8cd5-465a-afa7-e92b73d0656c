import { z } from 'zod';

// Project Details Schema (Step 1)
export const projectDetailsSchema = z
  .object({
    name: z
      .string()
      .min(1, 'Project name is required')
      .min(3, 'Project name must be at least 3 characters')
      .max(100, 'Project name must be less than 100 characters'),

    code: z
      .string()
      .min(1, 'Quotation number is required')
      .min(3, 'Quotation number must be at least 3 characters')
      .max(20, 'Quotation number must be less than 20 characters')
      .regex(
        /^[A-Z0-9-]+$/,
        'Quotation number must contain only uppercase letters, numbers, and hyphens',
      ),

    agency_id: z.string().min(1, 'Agency is required'),

    jkr_pic_id: z.string().min(1, 'JKR Person in Charge is required'),

    user_ids: z.array(z.string()).optional().default([]),

    location: z.string().min(1, 'Location is required'),

    state: z
      .enum([
        'JH',
        'KD',
        'KT',
        'ML',
        'NS',
        'PH',
        'PN',
        'PK',
        'PL',
        'SB',
        'SW',
        'SL',
        'TR',
        'WP',
        'LBN',
        'PW',
        'OTH',
      ])
      .optional()
      .refine((val) => val !== undefined, {
        message: 'State is required',
      }),

    start_date: z
      .string()
      .min(1, 'Start date is required')
      .refine((date) => {
        const selectedDate = new Date(date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return selectedDate >= today;
      }, 'Start date cannot be in the past'),

    end_date: z.string().optional().or(z.literal('')),

    status: z
      .enum(['pending', 'active', 'completed', 'cancelled'])
      .default('pending'),

    description: z
      .string()
      .max(500, 'Description must be less than 500 characters')
      .optional()
      .or(z.literal('')),
  })
  .refine(
    (data) => {
      if (data.end_date && data.end_date !== '') {
        return new Date(data.end_date) > new Date(data.start_date);
      }
      return true;
    },
    {
      message: 'End date must be after start date',
      path: ['end_date'],
    },
  );

// Competent Person Schema - Individual person for array usage
const competentPersonSchema = z.object({
  name: z
    .string()
    .min(1, 'Competent person name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),

  phone_number: z
    .string()
    .min(1, 'Phone number is required')
    .regex(
      /^(\+?6?01[0-46-9]-*[0-9]{7,8})$/,
      'Please enter a valid Malaysian phone number',
    ),

  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),

  registration_cert_file: z
    .instanceof(File)
    .optional()
    .or(z.undefined())
    .refine((file) => {
      if (!file) return true;
      return file.size <= 5 * 1024 * 1024; // 5MB
    }, 'File size must be less than 5MB')
    .refine((file) => {
      if (!file) return true;
      return ['application/pdf', 'image/jpeg', 'image/png'].includes(file.type);
    }, 'Only PDF, JPG, and PNG files are allowed'),

  lif_list_files: z
    .array(z.instanceof(File))
    .optional()
    .or(z.undefined())
    .refine((files) => {
      if (!files || files.length === 0) return true;
      return files.every((file) => file.size <= 5 * 1024 * 1024);
    }, 'Each file size must be less than 5MB')
    .refine((files) => {
      if (!files || files.length === 0) return true;
      return files.every((file) =>
        ['application/pdf', 'image/jpeg', 'image/png'].includes(file.type),
      );
    }, 'Only PDF, JPG, and PNG files are allowed'),
});

// Competent Persons Schema (Step 2)
export const competentPersonsSchema = z.object({
  competent_persons: z
    .array(competentPersonSchema)
    .min(1, 'At least one competent person is required')
    .max(10, 'Maximum 10 competent persons allowed per project'),
});

// PMA Schema - Individual PMA for array usage
const pmaSchema = z.object({
  pma_number: z
    .string()
    .min(1, 'PMA number is required')
    .max(50, 'PMA number must be less than 50 characters')
    .transform((val) => val.trim())
    .refine((val) => val.length > 0, {
      message: 'PMA number cannot be empty',
    }),

  expiry_date: z
    .string()
    .min(1, 'PMA expiry date is required')
    .refine((date) => {
      const selectedDate = new Date(date);
      const today = new Date();
      return selectedDate > today;
    }, 'PMA expiry date must be in the future'),

  location: z
    .string()
    .min(1, 'LIF location is required')
    .max(500, 'Location must be less than 500 characters'),

  competent_person_id: z
    .string()
    .min(1, 'Competent person is required')
    .uuid('Invalid competent person ID'),

  file: z
    .instanceof(File, { message: 'PMA certificate file is required' })
    .refine((file) => {
      return file.size <= 10 * 1024 * 1024; // 10MB
    }, 'File size must be less than 10MB')
    .refine((file) => {
      return ['application/pdf'].includes(file.type);
    }, 'Only PDF files are allowed'),
});

// PMAs Schema (Step 3)
export const pmasSchema = z.object({
  pmas: z
    .array(pmaSchema)
    .min(1, 'At least one PMA is required')
    .max(5, 'Maximum 5 PMAs allowed per project'),
});

// Combined schema for the complete form
export const projectFormSchema = projectDetailsSchema.and(
  z.object({
    competentPersons: competentPersonsSchema,
    pmas: pmasSchema,
  }),
);

export type ProjectDetailsSchema = z.infer<typeof projectDetailsSchema>;
export type CompetentPersonsSchema = z.infer<typeof competentPersonsSchema>;
export type PmasSchema = z.infer<typeof pmasSchema>;
export type ProjectFormSchema = z.infer<typeof projectFormSchema>;
